<template>
  <div class="responsibility-list">
    <div v-if="loading" class="loading-container">
      <div class="loading-text">正在加载表单列表...</div>
    </div>
    
    <div v-else class="form-list">
      <div
        v-for="(item, index) in formList"
        :key="index"
        class="form-item"
      >
        <div class="form-info">
          <h3 class="form-title">{{ item.formName }}</h3>
        </div>
        <van-button
          type="primary"
          size="small"
          @click="fillForm(item)"
        >
          填写
        </van-button>
      </div>
      
      <div v-if="!loading && formList.length === 0" class="empty-container">
        <div class="empty-text">暂无表单数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import { Button } from "vant";
import { writeLog, LogLevel } from "@/utils/logger";
import { getFormTypeList } from "@/api/index";

export default {
  name: "ResponsibilityList",
  components: {
    "van-button": Button,
  },
  data() {
    return {
      formList: [],
      loading: false,
      deptId: "" // 存储从entrance页面传递过来的deptId
    };
  },
  onLoad(options) {
    // 接收从entrance页面传递过来的deptId
    if (options && options.deptId) {
      this.deptId = decodeURIComponent(options.deptId);
      writeLog(LogLevel.INFO, "服务履责列表页", "接收到deptId", {
        deptId: this.deptId
      });
    }

    this.fetchFormTypeList();
  },
  methods: {
    async fetchFormTypeList() {
      try {
        this.loading = true;
        writeLog(LogLevel.INFO, "服务履责列表页", "开始获取表单类型列表");
        
        const response = await getFormTypeList({});
        
        writeLog(LogLevel.INFO, "服务履责列表页", "获取表单类型列表成功", {
          response: JSON.stringify(response)
        });
        
        // 根据接口返回的数据结构处理
        if (response && response.data) {
          this.formList = response.data;
        } else if (response && Array.isArray(response)) {
          this.formList = response;
        } else {
          this.formList = [];
        }
        
      } catch (error) {
        writeLog(LogLevel.ERROR, "服务履责列表页", "获取表单类型列表失败", {
          error: error.message
        });
        console.error("获取表单类型列表失败:", error);
        
        let errorMsg = "获取表单列表失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },
    
    async fillForm(item) {
      try {
        writeLog(LogLevel.INFO, "服务履责列表页", "点击填写表单", {
          formId: item.formId,
          formName: item.formName
        });

        // 构建跳转参数，直接传递基本信息，让form页面统一处理接口调用
        const navigationParams = {
          formId: item.formId,
          formName: encodeURIComponent(item.formName),
          formType: encodeURIComponent(item.formType || "")
        };

        // 将deptId传递给form页面
        if (this.deptId) {
          navigationParams.deptId = encodeURIComponent(this.deptId);
        }

        // 构建URL参数字符串
        const urlParams = Object.entries(navigationParams)
          .map(([key, value]) => `${key}=${value}`)
          .join('&');

        // 跳转到表单页面
        uni.navigateTo({
          url: `/pages/responsibility/form?${urlParams}`,
        });

      } catch (error) {
        writeLog(LogLevel.ERROR, "服务履责列表页", "填写表单失败", {
          error: error.message,
          formId: item.formId
        });
        console.error("填写表单失败:", error);

        uni.showToast({
          title: "操作失败",
          icon: "none",
        });
      }
    },
    
    goBack() {
      try {
        writeLog(LogLevel.INFO, "服务履责列表页", "返回上一页");
        uni.navigateBack();
      } catch (error) {
        console.error("返回失败:", error);
        uni.showToast({
          title: "返回失败",
          icon: "error",
        });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.responsibility-list {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
  padding-bottom: 100px;
}

.form-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-info {
  flex: 1;
}

.form-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 5px 0;
}

.form-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.form-item .van-button {
  margin-left: 15px;
  width: 60px;
  height: 36px;
}

.return-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}
</style> 