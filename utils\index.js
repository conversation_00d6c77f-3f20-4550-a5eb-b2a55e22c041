import { useUserStore } from '@/store/modules/user';

// 判断包含符号
export function containsCharacter(str, char) {
  const type = getFormTypeLabel(str);
  return type.includes(char);
}

// 根据formType获取表单类型
export function getFormTypeLabel(typeNumber) {
  switch (typeNumber) {
    case '1':
      return '日常巡视';
    case '2':
      return '特殊巡视';
    case '3':
      return '日常走访';
    case '4':
      return '特殊走访';
    case '5':
      return '工单走访';
    case '6':
      return '工单巡视';
    case '7':
      return '默认走访';
    case '8':
      return '默认巡视';
    default:
      return '未知类型'; // 在没有匹配任何已知类型的情况下返回一个默认消息
  }
}

// 根据类型判断编号label;
export function getFormTypeLabelByType(typeNumber) {
  switch (typeNumber) {
    case '1':
      return '巡视编号';
    case '2':
      return '巡视编号';
    case '3':
      return '走访编号';
    case '4':
      return '走访编号';
    case '5':
      return '工单编号';
    case '6':
      return '工单编号';
    case '7':
      return '走访编号';
    case '8':
      return '巡视编号';
    default:
      return '未知类型'; // 在没有匹配任何已知类型的情况下返回一个默认消息
  }
}

export function getUrlParams(urlString) {
  // 创建一个URL对象
  const url = new URL(urlString);

  // 使用URLSearchParams获取查询字符串参数
  const params = new URLSearchParams(url.search);

  // 创建一个空对象以存储参数
  const result = {};

  // 遍历所有的键值对并添加到结果对象中
  for (let [key, value] of params.entries()) {
    result[key] = isNaN(Number(value)) ? value : Number(value);
  }

  return result;
}

/**
 * 创建一个防抖动函数，该函数在被连续调用后，只会执行一次。
 *
 * @param {Function} func - 需要执行的函数
 * @param {number} wait - 等待时间 (毫秒)
 * @param {boolean} immediate - 如果为true, 则会在延迟开始时调用函数，否则在延迟结束后调用
 * @return {Function} 返回一个被防抖动的函数
 */
export function debounce(func, wait, immediate) {
  let timeout;

  return function () {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const context = this;
    const args = arguments;

    const later = function () {
      timeout = null;
      if (!immediate) {
        func.apply(context, args);
      }
    };

    const callNow = immediate && !timeout;

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) {
      func.apply(context, args);
    }
  };
}

/**
 * 创建一个节流函数，该函数在指定的时间间隔内最多执行一次函数。
 *
 * @param {Function} func - 需要执行的函数
 * @param {number} wait - 执行间隔时间 (毫秒)
 * @return {Function} 返回一个被节流的函数
 */
export function throttle(func, wait) {
  let context, args;
  let previous = 0;

  return function () {
    const now = new Date().getTime();

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    context = this;
    args = arguments;

    if (now - previous > wait) {
      func.apply(context, args);
      previous = now;
    }
  };
}

// 按钮权限
export function hasPermi(perm) {
  const userStore = useUserStore();
  return userStore.getPermissions.includes(perm);
}
