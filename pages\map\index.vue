<template>
  <view class="map-container">
    <div id="sgMap" class="map-box"></div>
    <div id="panel" class="result-panel"></div>

    <!-- 控制按钮区 -->
    <div class="map-ctrl-wrap">
      <div class="line-wrap">
        <van-button class="reroute-btn" @click="replanRoute"
          >重新规划路径</van-button
        >
        <van-button
          class="mode-switch-btn"
          @click="switchNavigationMode"
          :type="currentMode === 'walking' ? 'default' : 'primary'"
        >
          {{ currentMode === "walking" ? "切换驾车" : "切换步行" }}
        </van-button>
      </div>
    </div>

    <div
      v-if="loading"
      class="w-full h-full text-white fixed top-0 left-0 bg-black opacity-50 z-50 flex justify-center items-center"
    >
      <van-loading type="spinner" color="#fff" size="24px" />
      加载中...
    </div>
  </view>
</template>

<script>
import { Loading } from "vant";
import { isProd } from "@/utils/env";

export default {
  components: {
    "van-loading": Loading,
  },
  data() {
    return {
      loading: true,
      map: null,
      walkingTask: null,
      directionsTask: null,
      geocodingPlusTask: null,
      key: "29157aa48d4739d297756afb1395ea81",
      secret: "dcf32339ed5f398ba00358c38cf33e47",
      plugin: [
        "SGMap.GeolocationTask",
        "SGMap.WalkingPlusTask",
        "SGMap.GeocodingPlusTask",
        "SGMap.DirectionsTask",
      ],
      endPoint: null,
      currentMode: "walking", // 当前导航模式：walking 或 driving
      agentId: navigator.userAgent.toLowerCase().includes("uat")
        ? "1006802"
        : "1001657",
    };
  },
  onLoad(options) {
    console.log("[地图] onLoad options:", options);
    if (isProd()) {
      console.log("[地图] 生产环境不加载地图组件");
      uni.navigateBack();
      return;
    }

    // 获取路由参数
    this.address = options.address;
    this.cityName = options.cityName;

    // 处理城市名称，如果是"中国"或为空，则从地址中提取城市名
    if (!this.cityName || this.cityName === "中国") {
      this.cityName = this.extractCityFromAddress(this.address);
      console.log("[地图] 从地址中提取城市名:", this.cityName);
    }

    // 验证必要参数
    if (!this.address) {
      console.warn("[地图] 缺少必要参数:", {
        address: this.address,
        cityName: this.cityName,
      });
      uni.showToast({
        title: "地址信息不完整",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
      return;
    }

    // 加载地图脚本
    const script = document.createElement("script");
    script.src = "https://map.sgcc.com.cn/maps?v=3.0.0";
    document.head.appendChild(script);
    script.onload = () => {
      console.log("[地图] 地图脚本加载成功");
      this.initLogin();
    };
    script.onerror = (error) => {
      console.error("[地图] 地图脚本加载失败:", error);
      uni.showToast({
        title: "地图加载失败",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
    };
  },
  methods: {
    // 从地址中提取城市名
    extractCityFromAddress(address) {
      if (!address) return "襄阳"; // 默认城市

      // 常见的城市名模式匹配
      const cityPatterns = [
        /(\w+市)/, // 匹配"XX市"
        /(\w+县)/, // 匹配"XX县"
        /(\w+区)/, // 匹配"XX区"
      ];

      for (const pattern of cityPatterns) {
        const match = address.match(pattern);
        if (match) {
          const cityName = match[1];
          // 如果是区，尝试找到对应的市
          if (cityName.endsWith("区")) {
            const cityMatch = address.match(/(\w+市)/);
            if (cityMatch) {
              return cityMatch[1];
            }
          }
          return cityName;
        }
      }

      return "襄阳"; // 默认返回襄阳
    },

    async initLogin() {
      console.log("[地图] 开始初始化登录");
      try {
        console.log("[地图] 尝试登录 key:", this.key);
        await SGMap.tokenTask.login(this.key, this.secret);
        console.log("[地图] 登录成功，加载插件:", this.plugin);
        await SGMap.plugin(this.plugin);
        console.log("[地图] 插件加载成功");
        this.geocodingPlusTask = new SGMap.GeocodingPlusTask({
          city: this.cityName || "襄阳", // 使用传入的城市或默认值
          extensions: "base",
        });

        // 初始化驾车导航任务
        this.directionsTask = new SGMap.DirectionsTask();
        console.log("[地图] 驾车导航任务初始化成功");

        this.initMap();
      } catch (e) {
        console.error("[地图] 初始化失败:", e);
        uni.showToast({
          title: "地图初始化失败",
          icon: "none",
          duration: 2000,
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
        throw e;
      }
    },
    initMap() {
      console.log("[地图] 开始初始化地图");
      this.map = new SGMap.Map({
        container: "sgMap",
        style: "aegis://styles/aegis/Streets-v2",
        zoom: 11,
        center: [116.306007, 39.979771],
        localIdeographFontFamily: "Microsoft YaHei",
      });

      this.map.on("load", async () => {
        console.log("[地图] 地图加载完成，获取当前位置");
        try {
          const data = await new SGMap.GeolocationTask().getLocation();
          console.log("[地图] 当前位置:", data[0]);
          this.inited(data[0]);
        } catch (e) {
          console.error("[地图] 获取位置失败:", e);
        }
      });
    },
    inited(startPoint) {
      console.log("[地图] 初始化导航任务, startPoint:", startPoint);
      this.walkingTask = new SGMap.WalkingPlusTask({
        map: this.map,
        panel: "panel", // 添加结果面板
      });
      this.search(startPoint);
    },
    async search(startPoint) {
      console.log("[地图] 开始搜索路径, startPoint:", startPoint);
      try {
        // 添加参数验证
        if (!this.address || !this.cityName) {
          console.error("[地图] 终点地址或城市为空:", {
            address: this.address,
            cityName: this.cityName,
          });
          uni.showToast({
            title: "终点地址不能为空",
            icon: "none",
            duration: 2000,
          });
          this.loading = false;
          return;
        }

        const { regeocodes } = await this.geocodingPlusTask.getAddress(
          startPoint
        );
        console.log("[地图] 地理编码结果:", regeocodes);

        // 检查起点和终点是否在同一城市或相邻区域
        const startCity = regeocodes[0].addressComponent.city;
        const endCity = this.cityName;

        console.log("[地图] 城市检查:", {
          起点城市: startCity,
          终点城市: endCity,
        });

        await new Promise((resolve, reject) => {
          console.log("[地图] 开始路径规划:", {
            起点地址: regeocodes[0].formatted_address,
            起点城市: regeocodes[0].addressComponent.city,
            终点地址: this.address,
            终点城市: this.cityName,
          });

          // 确保地址不为空
          if (!regeocodes[0].formatted_address || !this.address) {
            reject(new Error("起点或终点地址为空"));
            return;
          }

          this.walkingTask.search({
            points: [
              {
                keyword: regeocodes[0].formatted_address,
                city: regeocodes[0].addressComponent.city,
                location: startPoint.join(","), // 使用字符串格式的坐标
              },
              {
                keyword: this.address,
                city: this.cityName,
              },
            ],
            complete: (status, result) => {
              console.log("[地图] 路径规划状态:", status);
              console.log("[地图] 路径规划结果:", result);

              if (result?.status === "1" && result?.info === "ok") {
                try {
                  // 修改：从route.destination获取终点坐标
                  if (!result.route?.destination) {
                    console.log(
                      "[地图] 尝试从route.destination获取坐标失败，尝试其他方式"
                    );
                    // 尝试从paths中获取最后一个点
                    if (result.route?.paths?.[0]?.steps?.length > 0) {
                      const lastStep =
                        result.route.paths[0].steps[
                          result.route.paths[0].steps.length - 1
                        ];
                      if (lastStep.end_location) {
                        this.endPoint = {
                          lng: Number(lastStep.end_location.split(",")[0]),
                          lat: Number(lastStep.end_location.split(",")[1]),
                        };
                        console.log(
                          "[地图] 从steps中获取到终点坐标:",
                          this.endPoint
                        );
                        resolve(result);
                        return;
                      }
                    }
                    throw new Error("未找到目标坐标");
                  }

                  const [lng, lat] = result.route.destination
                    .split(",")
                    .map(Number);

                  if (!isNaN(lng) && !isNaN(lat)) {
                    this.endPoint = { lng, lat };
                    console.log("[地图] 成功解析终点坐标:", this.endPoint);
                    resolve(result);
                  } else {
                    throw new Error("坐标解析失败");
                  }
                } catch (err) {
                  console.error("[地图] 坐标处理错误:", err);
                  reject(err);
                }
              } else {
                const errorMsg = result?.info || "路径规划失败";
                console.error("[地图] 路径规划失败:", errorMsg);

                // 特殊处理OVER_DIRECTION_RANGE错误，尝试驾车导航
                if (errorMsg === "OVER_DIRECTION_RANGE") {
                  console.log("[地图] 步行导航距离超限，尝试驾车导航");
                  reject(new Error("WALKING_OVER_RANGE"));
                } else {
                  reject(new Error(this.getErrorMessage(errorMsg)));
                }
              }
            },
          });
        });

        this.loading = false;
      } catch (e) {
        console.error("[地图] 搜索过程错误:", e);

        // 如果是步行导航距离超限，尝试驾车导航
        if (e.message === "WALKING_OVER_RANGE") {
          console.log("[地图] 开始尝试驾车导航");
          try {
            this.currentMode = "driving"; // 切换到驾车模式
            await this.searchByDriving(startPoint);
          } catch (drivingError) {
            console.error("[地图] 驾车导航也失败:", drivingError);
            this.currentMode = "walking"; // 恢复步行模式
            this.showAlternativeOptions();
          }
        } else if (
          e.message.includes("OVER_DIRECTION_RANGE") ||
          e.message.includes("导航距离超出限制")
        ) {
          this.showAlternativeOptions();
        } else {
          uni.showToast({
            title: e.message || "获取路径失败",
            icon: "none",
            duration: 3000,
          });
        }
        this.loading = false;
      }
    },

    // 驾车导航搜索
    async searchByDriving(startPoint) {
      console.log("[地图] 开始驾车路径规划, startPoint:", startPoint);

      try {
        // 获取起点地址信息
        const { regeocodes } = await this.geocodingPlusTask.getAddress(
          startPoint
        );
        console.log("[地图] 驾车导航 - 地理编码结果:", regeocodes);

        // 首先需要获取终点坐标
        console.log("[地图] 开始解析终点地址:", this.address);

        // 解析终点地址获取坐标（复用现有的geocodingPlusTask）
        const endResult = await this.geocodingPlusTask.getLocation(
          this.address
        );
        console.log("[地图] 终点地址解析结果:", endResult);

        if (
          !endResult ||
          !endResult.geocodes ||
          endResult.geocodes.length === 0 ||
          !endResult.geocodes[0].location
        ) {
          throw new Error("无法解析终点地址坐标");
        }

        // 确保location格式正确（经度,纬度）
        const locationStr = endResult.geocodes[0].location;
        const endPoint = locationStr
          .split(",")
          .map((coord) => parseFloat(coord.trim()));

        if (endPoint.length !== 2 || isNaN(endPoint[0]) || isNaN(endPoint[1])) {
          throw new Error("终点坐标格式错误");
        }

        console.log("[地图] 解析得到终点坐标:", endPoint);

        // 使用驾车导航API（根据文档添加必要参数）
        const result = await this.directionsTask.searchByDriving({
          startPoint: startPoint,
          endPoint: endPoint,
          waypointsPast: [], // 途经点数组，即使为空也要传递
          policy: "", // 推荐路线
        });

        console.log("[地图] 驾车导航结果:", result);

        if (result && result.routes && result.routes[0]) {
          // 清除之前的步行路径
          this.clearWalkingRoute();

          // 显示驾车路径
          this.displayDrivingRoute(result.routes[0]);

          // 保存终点坐标
          this.endPoint = {
            lng: endPoint[0],
            lat: endPoint[1],
          };

          uni.showToast({
            title: "已切换为驾车导航",
            icon: "success",
            duration: 2000,
          });

          this.loading = false;
        } else {
          throw new Error("驾车路径规划失败");
        }
      } catch (error) {
        console.error("[地图] 驾车导航失败:", error);
        throw error;
      }
    },

    // 清除步行路径
    clearWalkingRoute() {
      try {
        // 清除步行导航的显示
        if (this.walkingTask && this.walkingTask.clear) {
          this.walkingTask.clear();
          console.log("[地图] 清除步行路径");
        } else {
          // 如果walkingTask没有clear方法，尝试清除可能的步行路径图层
          if (this.map && this.map.getSource) {
            const walkingSources = [
              "walking-route",
              "walking-path",
              "route-line",
            ];
            walkingSources.forEach((sourceId) => {
              if (this.map.getSource(sourceId)) {
                try {
                  if (this.map.getLayer(sourceId + "-layer")) {
                    this.map.removeLayer(sourceId + "-layer");
                  }
                  this.map.removeSource(sourceId);
                } catch (e) {
                  // 忽略删除不存在图层的错误
                }
              }
            });
          }
          console.log("[地图] 尝试清除步行路径图层");
        }
      } catch (error) {
        console.error("[地图] 清除步行路径失败:", error);
      }
    },

    // 显示驾车路径
    displayDrivingRoute(route) {
      try {
        console.log("[地图] 显示驾车路径:", route);

        // 验证路径数据
        if (!route || !route.feature) {
          throw new Error("路径数据无效");
        }

        // 验证地图对象
        if (!this.map || !this.map.addSource || !this.map.addLayer) {
          throw new Error("地图对象未初始化");
        }

        // 根据思极地图的API显示驾车路径
        const routeData = route.feature;

        // 添加路径图层
        if (!this.map.getSource("driving-route")) {
          this.map.addSource("driving-route", {
            type: "geojson",
            data: routeData,
          });

          this.map.addLayer({
            id: "driving-route-line",
            type: "line",
            source: "driving-route",
            layout: {
              "line-cap": "round",
              "line-join": "round",
            },
            paint: {
              "line-color": "#1e98ff",
              "line-width": 5,
              "line-opacity": 0.8,
            },
          });
        } else {
          this.map.getSource("driving-route").setData(routeData);
        }

        // 调整地图视野以显示完整路径
        if (
          routeData.geometry &&
          routeData.geometry.coordinates &&
          Array.isArray(routeData.geometry.coordinates) &&
          routeData.geometry.coordinates.length > 0
        ) {
          const coordinates = routeData.geometry.coordinates;
          const bounds = this.calculateBounds(coordinates);
          if (this.map.fitBounds) {
            this.map.fitBounds(bounds, { padding: 50 });
          }
        }
      } catch (error) {
        console.error("[地图] 显示驾车路径失败:", error);
        throw error; // 重新抛出错误以便上层处理
      }
    },

    // 计算路径边界
    calculateBounds(coordinates) {
      let minLng = Infinity,
        maxLng = -Infinity;
      let minLat = Infinity,
        maxLat = -Infinity;

      coordinates.forEach((coord) => {
        const [lng, lat] = coord;
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      });

      return [
        [minLng, minLat],
        [maxLng, maxLat],
      ];
    },

    // 获取友好的错误信息
    getErrorMessage(errorCode) {
      const errorMessages = {
        OVER_DIRECTION_RANGE: "导航距离超出限制",
        NO_ROADS: "无法找到可行路径",
        OVER_QUOTA: "请求次数超限",
        INVALID_PARAMS: "参数错误",
        UNKNOWN_ERROR: "未知错误",
      };
      return errorMessages[errorCode] || `路径规划失败: ${errorCode}`;
    },

    // 显示替代方案
    showAlternativeOptions() {
      uni.showModal({
        title: "导航提示",
        content:
          "当前距离较远，超出步行导航范围。建议使用其他导航应用进行驾车或公交导航。",
        showCancel: true,
        cancelText: "返回",
        confirmText: "打开外部导航",
        success: (res) => {
          if (res.confirm) {
            this.openExternalNavigation();
          } else {
            uni.navigateBack();
          }
        },
      });
    },

    // 打开外部导航应用
    openExternalNavigation() {
      // 尝试打开系统默认地图应用
      const encodedAddress = encodeURIComponent(this.address);
      const mapUrls = [
        `baidumap://map/direction?destination=${encodedAddress}`, // 百度地图
        `iosamap://path?dname=${encodedAddress}`, // 高德地图
        `geo:0,0?q=${encodedAddress}`, // 通用地理位置
      ];

      // 尝试打开第一个可用的地图应用
      let opened = false;
      for (const url of mapUrls) {
        try {
          window.location.href = url;
          opened = true;
          break;
        } catch (e) {
          console.log(`尝试打开 ${url} 失败:`, e);
        }
      }

      if (!opened) {
        uni.showToast({
          title: "无法打开外部导航应用",
          icon: "none",
          duration: 2000,
        });
      }

      // 延迟返回，给外部应用打开的时间
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    },
    async replanRoute() {
      console.log("[地图] 开始重新规划路径");
      this.loading = true;
      try {
        const data = await new SGMap.GeolocationTask().getLocation();
        console.log("[地图] 重新获取当前位置:", data[0]);
        await this.search(data[0]);
      } catch (e) {
        console.error("[地图] 重新规划路径失败:", e);
        uni.showToast({
          title: "重新规划失败",
          icon: "none",
        });
      }
      this.loading = false;
    },

    // 切换导航模式
    async switchNavigationMode() {
      console.log("[地图] 切换导航模式, 当前模式:", this.currentMode);

      if (this.loading) {
        uni.showToast({
          title: "正在处理中，请稍候",
          icon: "none",
          duration: 1000,
        });
        return;
      }

      this.loading = true;

      try {
        // 获取当前位置
        const data = await new SGMap.GeolocationTask().getLocation();
        const startPoint = data[0];

        if (this.currentMode === "walking") {
          // 切换到驾车模式
          console.log("[地图] 切换到驾车导航");
          this.currentMode = "driving";
          await this.searchByDriving(startPoint);
        } else {
          // 切换到步行模式
          console.log("[地图] 切换到步行导航");
          this.currentMode = "walking";
          this.clearDrivingRoute();
          await this.search(startPoint);
        }
      } catch (error) {
        console.error("[地图] 切换导航模式失败:", error);
        uni.showToast({
          title: "切换失败: " + (error.message || "未知错误"),
          icon: "none",
          duration: 2000,
        });
      }

      this.loading = false;
    },

    // 清除驾车路径
    clearDrivingRoute() {
      try {
        if (
          this.map &&
          this.map.getSource &&
          this.map.getSource("driving-route")
        ) {
          // 先检查图层是否存在再删除
          if (this.map.getLayer && this.map.getLayer("driving-route-line")) {
            this.map.removeLayer("driving-route-line");
          }
          this.map.removeSource("driving-route");
          console.log("[地图] 清除驾车路径");
        }
      } catch (error) {
        console.error("[地图] 清除驾车路径失败:", error);
      }
    },

    openMapApp(address, latitude, longitude) {
      console.log("[地图] 打开地图应用:", {
        address,
        latitude,
        longitude,
        agentId: this.agentId,
      });
      const url = encodeURI(
        `zipapp://appid.${this.agentId}/index.html?scheme=route&dname=${address}&dlat=${latitude}&dlon=${longitude}&closeWithEndNavi=1`
      );

      wx.invoke(
        "multiWindows_startWidget",
        {
          agentId: this.agentId,
          window: {
            windowId: "sgmap_app",
            url: url,
            openType: 1,
            showAppBar: "false",
          },
        },
        (res) => {
          console.log("打开思极地图微应用 res = ", res);
          uni.navigateBack();
        }
      );
    },
  },
};
</script>

<style lang="scss">
.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  .map-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .result-panel {
    position: fixed;
    background-color: white;
    max-height: 80vh;
    top: calc(44px + 10px); // 导航栏高度 + 间距
    right: 10px;
    width: 280px;
    z-index: 100;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    overflow-y: auto;
  }

  .map-ctrl-wrap {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom, 20px) + 20px);
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;

    .line-wrap {
      display: flex;
      justify-content: center;
      gap: 10px;
      padding: 10px;

      .reroute-btn {
        background: #4caf50;
        color: white;
        border-radius: 4px;
        padding: 0 20px;
      }

      .mode-switch-btn {
        border-radius: 4px;
        padding: 0 20px;
      }
    }
  }
}

// 隐藏uni-app默认导航栏
::v-deep {
  .uni-page-head {
    background: transparent !important;
  }
  .uni-page-head-bd {
    opacity: 0;
  }
}
</style>
