<template>
  <div class="responsibility-entrance">
    <div class="header">
      <h1 class="title">"1122"服务履责</h1>
    </div>
    
    <div class="content">
      <div class="requirements">
        <h3>服务履责要求：</h3>
        <div class="requirement-list">
          <p>1. 公司专业部门(供指中心、营销部、配电部、安监部)部门负责人每周至少1次到班站所或到现场检查督导。</p>
          <p>2. 供电单位党政主要负责人每周至少1次到班站所或到现场检查督导。</p>
          <p>3. 供电单位分管业务领导每周至少2次到班站所或到现场检查督导。</p>
          <p>4. 供电单位专业部门(供指分中心、营销部(营销专业业室)、运检部(配电运检)、安监部)部负责人每周至少2次到班站所或到现场、班站所负责人每周范少2次到现场检查督导。</p>
          <p>5. 公司专业部门每年履责足迹需要覆盖所有供电单位;供电单位党政主要负责人、分管业务领导、专业部门每年履责足迹需要覆盖所有班站所。</p>
        </div>
      </div>
      
      <div class="management-info">
        <h3>履责班组：</h3>
        <div class="info-item">
          <span class="label">{{ responsibilityName }}</span>
        </div>
      </div>
    </div>
    
    <div class="entrance-button">
      <van-button type="primary" block size="large" @click="enterService">
        服务履责入口
      </van-button>
    </div>
  </div>
</template>

<script>
import { Button } from "vant";
import { writeLog, LogLevel } from "@/utils/logger";

export default {
  name: "ResponsibilityEntrance",
  components: {
    "van-button": Button,
  },
  data() {
    return {
      responsibilityName: "", // 默认值，会被扫码数据覆盖
      scanData: null,
      deptId: "", // 存储扫码返回的deptId
    };
  },
  onLoad(options) {
    this.initPageData(options);
  },
  methods: {
    initPageData(options) {
      try {
        writeLog(LogLevel.INFO, "服务履责入口页", "初始化页面数据", { options });

        // 处理从扫码页面传递过来的扫码数据
        if (options.scanData) {
          const scanResponse = JSON.parse(decodeURIComponent(options.scanData));
          this.scanData = scanResponse;

          // 从扫码数据中提取履责名称和deptId
          // 根据实际接口返回的数据结构调整字段名
          if (scanResponse.data) {
            // 根据履责扫码接口返回的数据结构，使用 deptName 字段
            this.responsibilityName =
              scanResponse.data.deptName ||
              scanResponse.data.orgName ||
              scanResponse.data.consName ||
              scanResponse.data.tgName ||
              scanResponse.data.name ||
              scanResponse.data.title ||
              ""; // 如果都没有，使用空字符串

            // 提取deptId字段，用于后续表单提交
            this.deptId = scanResponse.data.deptId || "";
          }

          writeLog(LogLevel.INFO, "服务履责入口页", "解析扫码数据成功", {
            responsibilityName: this.responsibilityName,
            deptId: this.deptId,
            scanData: scanResponse
          });
        }
      } catch (error) {
        writeLog(LogLevel.ERROR, "服务履责入口页", "初始化页面数据失败", {
          error: error.message
        });
        console.error("解析扫码数据失败:", error);

        // 解析失败时使用默认值
        this.responsibilityName = "";

        uni.showToast({
          title: "数据解析失败",
          icon: "none",
        });
      }
    },

    enterService() {
      try {
        writeLog(LogLevel.INFO, "服务履责入口页", "进入服务履责", {
          responsibilityName: this.responsibilityName,
          deptId: this.deptId
        });

        // 将deptId通过URL参数传递给list页面
        const deptIdParam = this.deptId ? `?deptId=${encodeURIComponent(this.deptId)}` : "";
        uni.navigateTo({
          url: `/pages/responsibility/list${deptIdParam}`,
        });
      } catch (error) {
        console.error("进入服务履责失败:", error);
        uni.showToast({
          title: "操作失败",
          icon: "error",
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.responsibility-entrance {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
  padding-bottom: 100px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.requirements {
  margin-bottom: 30px;
}

.requirements h3 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.requirement-list {
  color: #666;
  line-height: 1.8;
  font-size: 14px;
}

.requirement-list p {
  margin-bottom: 10px;
  text-align: justify;
}

.management-info h3 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.entrance-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.entrance-button .van-button {
  background: #52c41a;
  border-color: #52c41a;
  font-size: 16px;
  font-weight: bold;
  height: 48px;
  border-radius: 8px;
}
</style>