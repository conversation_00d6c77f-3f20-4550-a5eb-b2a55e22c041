/** @type {import('tailwindcss').Config} */
export default {
  // https://ask.dcloud.net.cn/article/40098
  separator: "__", // 如果是小程序项目需要设置这一项，将 : 选择器替换成 __，之后 hover:bg-red-500 将改为 hover__bg-red-500
  corePlugins: {
    // 预设样式
    preflight: false, // 一般uniapp都有预设样式，所以不需要tailwindcss的预设
    // 以下功能小程序不支持
    space: false, // > 子节点选择器
    divideWidth: false,
    divideColor: false,
    divideStyle: false,
    divideOpacity: false,
  },
  content: [
    "./pages/**/*.{vue,js,jsx,ts,tsx}",
    "./components/**/*.{vue,js,jsx,ts,tsx}",
    "./layouts/**/*.{vue,js,jsx,ts,tsx}",
    "./App.vue",
    "./static/**/*.html",
  ],
  purge: {
    enabled: true,
    content: [
      "./pages/**/*.{vue,js,jsx,ts,tsx}",
      "./components/**/*.{vue,js,jsx,ts,tsx}",
      "./layouts/**/*.{vue,js,jsx,ts,tsx}",
      "./App.vue",
      "./static/**/*.html",
    ],
  },
  theme: {
    extend: {},
  },
  plugins: [],
};
