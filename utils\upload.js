import store from '@/store'
import config from '@/config'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'
import { URL_CODE_MAP } from '../static/codepass-urlCode'

let timeout = 10000
const baseUrl = config.baseUrl

const upload = config => {
  // 生产环境使用 window.Api
  if (process.env.NODE_ENV === 'production') {
    const urlCode = URL_CODE_MAP[config.url]
    if (!urlCode) {
      console.error('未找到对应的 urlCode:', config.url)
      return Promise.reject('未找到对应的 urlCode')
    }

    let userInfo = JSON.parse(sessionStorage.getItem('getUserInfo'))

    // 构造上传配置
    const apiConfig = {
      code: urlCode,
      type: 'notRequestBody', // 固定为 notRequestBody
      headers: {
        thirdId: 'd6d7623b46b843d78d1c1e8ccca38314',
        token: userInfo?.authToken
      },
      // 自定义传输 params
      ...config.params,
      // 文件相关配置
      formData: config.formData,
      file: config.filePath
    }

    return window.Api.upload(apiConfig)
  }

  // 开发环境保持原有逻辑
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken()
  }
  // get请求映射params参数
  if (config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.url = url
  }
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      timeout: config.timeout || timeout,
      url: baseUrl + config.url,
      filePath: config.filePath,
      name: config.name || 'file',
      header: config.header,
      formData: config.formData,
      success: (res) => {
        let result = JSON.parse(res.data)
        const code = result.code || 200
        const msg = errorCode[code] || result.msg || errorCode['default']
        if (code === 200) {
          resolve(result)
        } else if (code == 401) {
          showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then(res => {
            if (res.confirm) {
              store.dispatch('LogOut').then(res => {
                uni.reLaunch({ url: '/pages/login/login' })
              })
            }
          })
          reject('无效的会话，或者会话已过期，请重新登录。')
        } else if (code === 500) {
          toast(msg)
          reject('500')
        } else if (code !== 200) {
          toast(msg)
          reject(code)
        }
      },
      fail: (error) => {
        let { message } = error
        if (message == 'Network Error') {
          message = '后端接口连接异常'
        } else if (message.includes('timeout')) {
          message = '系统接口请求超时'
        } else if (message.includes('Request failed with status code')) {
          message = '系统接口' + message.substr(message.length - 3) + '异常'
        }
        toast(message)
        reject(error)
      }
    })
  })
}

export default upload
