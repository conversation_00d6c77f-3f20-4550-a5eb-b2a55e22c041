<template>
  <view class="loading-container">
    <!-- 加载中状态 -->
    <template v-if="loginStatus === 'loading'">
      <van-loading type="spinner" color="#008c87" size="36" />
      <text class="loading-text">正在加载...</text>
    </template>

    <!-- 加载失败状态 -->
    <template v-if="loginStatus === 'error'">
      <van-icon name="close" size="36" color="#ff4d4f" />
      <text class="error-text">{{ errorMsg }}</text>
      <van-button
        type="primary"
        size="small"
        class="retry-btn"
        color="#008c87"
        @click="handleSSOLogin"
      >
        <van-icon name="replay" class="retry-icon" />
        重新加载
      </van-button>
    </template>
  </view>
</template>

<script>
import { loginEnvironment } from "@/api";
import { writeLog, LogLevel } from "@/utils/logger";
import { mockSSOParams } from "@/config/dev-sso-mock";

export default {
  data() {
    return {
      loginStatus: "loading", // loading, error
      errorMsg: "加载失败",
      retryCount: 0,
      maxRetries: 3,
      isLoading: false, // 添加加载状态标记
    };
  },
  created() {
    this.handleSSOLogin();
  },
  methods: {
    async handleSSOLogin() {
      if (this.isLoading) return;
      this.isLoading = true;

      try {
        writeLog(LogLevel.INFO, "SSO登录", "开始登录流程");
        this.loginStatus = "loading";

        // 添加1.5秒延迟
        await new Promise((resolve) => setTimeout(resolve, 1500));

        let params = {};

        // 严格区分开发环境和生产环境
        if (process.env.NODE_ENV === "development") {
          // 开发环境：使用mock数据
          writeLog(LogLevel.INFO, "SSO登录", "开发环境使用mock数据");
          params = mockSSOParams;
          params.userInfo = JSON.stringify(params.userInfo);
        } else {
          // 生产环境：使用URL参数（保持原有逻辑）
          const queryStr = window.location.search.substring(1);
          if (!queryStr) {
            writeLog(LogLevel.ERROR, "SSO登录", "URL参数获取失败");
            throw new Error("无法获取加载信息");
          }
          const pairs = queryStr.split("&");
          pairs.forEach((item) => {
            const keyValue = item.split("=");
            const key = decodeURIComponent(keyValue[0]);
            const value = decodeURIComponent(keyValue[1] || "");
            params[key] = value;
          });
        }

        // 检查userInfo参数
        if (!params.userInfo) {
          throw new Error("无法获取用户信息");
        }

        let userInfo;
        try {
          userInfo = JSON.parse(params.userInfo);
          // 存储用户信息到 sessionStorage
          sessionStorage.setItem("getUserInfo", params.userInfo);
          sessionStorage.setItem("loginName", userInfo.loginName || "admin"); // 确保设置loginName
        } catch (e) {
          throw new Error("用户信息格式错误");
        }

        writeLog(LogLevel.INFO, "SSO登录", "用户信息解析成功", { userInfo });

        const loginName = userInfo.loginName || "admin"; // 使用默认值
        if (!loginName) {
          writeLog(LogLevel.ERROR, "SSO登录", "缺少登录名");
          throw new Error("缺少必要的用户信息");
        }

        // 打印日志
        writeLog(LogLevel.INFO, "SSO登录", "处理后的登录名", { loginName });
        console.log("sanitizedLoginName:", loginName);

        const res = await loginEnvironment({ userName: loginName });
        writeLog(LogLevel.INFO, "SSO登录", "登录请求完成", {
          success: !!res.token,
        });

        // 登录成功后再分发 action
        if (res.token) {
          writeLog(LogLevel.INFO, "SSO登录", "登录成功，正在获取用户信息");
          await this.$store.dispatch("Login", { token: res.token });
          await this.$store.dispatch("GetInfo");
          writeLog(LogLevel.INFO, "SSO登录", "用户信息获取完成，准备跳转");
          this.$tab.reLaunch("/pages/index");
        } else {
          throw new Error("登录失败: 未获取到用户信息");
        }
      } catch (error) {
        writeLog(LogLevel.ERROR, "SSO登录", "登录过程出错", {
          retryCount: this.retryCount,
          errorMessage: error.message,
          errorResponse: error.response,
        });
        this.retryCount++;
        this.loginStatus = "error";

        // 统一错误信息处理 - 优先使用服务器返回的msg
        let errorMessage = "加载失败";
        if (error.response?.msg) {
          errorMessage = error.response.msg;
        } else if (
          error.message &&
          !error.message.includes("undefined") &&
          !error.message.includes("JSON")
        ) {
          errorMessage = error.message;
        }

        this.errorMsg =
          this.retryCount >= this.maxRetries
            ? "多次加载失败，请稍后再试"
            : errorMessage;

        console.error("加载失败:", error);
      } finally {
        this.isLoading = false;
      }
    },
  },
};
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f0f9f9;
  /* 浅绿背景 */
}

.loading-text {
  margin-top: 16px;
  color: #008c87;
  /* 主题色 */
  font-size: 16px;
}

.error-text {
  margin: 16px 0;
  color: #ff4d4f;
  /* 错误色保持醒目 */
  font-size: 14px;
}

.retry-btn {
  margin-top: 16px;
  border-radius: 20px;
  padding: 0 24px;
  background: #008c87 !important;
  /* 强制使用主题色 */
}

.retry-btn:active {
  background: #007873 !important;
  /* 点击时稍深 */
}

/* 添加简单的动画效果 */
.van-loading,
.van-icon {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
