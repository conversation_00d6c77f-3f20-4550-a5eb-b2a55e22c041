<template>
  <div class="board-container">
    <van-tabs v-model="active" shrink @change="onGetDashboardData">
      <van-tab title="走访" name="zf" />
      <van-tab title="巡视" name="xs" />
    </van-tabs>

    <div class="board-content">
      <div class="coverage-card">
        <div class="coverage-wrapper">
          <!-- 七日平均覆盖率 -->
          <div class="coverage-item">
            <div class="coverage-header">
              <span class="dot"></span>
              <span class="title">七日平均覆盖率</span>
            </div>
            <div class="coverage-value">{{ weekPro || 0 }}%</div>
          </div>

          <!-- 历史平均覆盖率 -->
          <div class="coverage-item">
            <div class="coverage-header">
              <span class="dot"></span>
              <span class="title">历史平均覆盖率</span>
            </div>
            <div class="coverage-value">{{ historyPro || 0 }}%</div>
          </div>
        </div>
      </div>

      <div class="charts-container">
        <div class="chart-section">
          <div class="section-header">
            <span class="header-line"></span>
            <span class="header-title"
              >近七日{{ active === "zf" ? "走访" : "巡视" }}覆盖率</span
            >
            <span class="header-tag">TOP5</span>
          </div>
          <div class="chart-wrapper">
            <div ref="weekChart" class="chart"></div>
          </div>
        </div>

        <div class="chart-section">
          <div class="section-header">
            <span class="header-line"></span>
            <span class="header-title"
              >历史{{ active === "zf" ? "走访" : "巡视" }}覆盖率</span
            >
            <span class="header-tag">TOP5</span>
          </div>
          <div class="chart-wrapper">
            <div ref="historyChart" class="chart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDashboardData } from "@/api";
import * as echarts from "echarts";

export default {
  name: "DashboardPage",
  data() {
    return {
      active: "zf",
      xsHistoryVO: null,
      xsWeekVO: null,
      zfHistoryVO: null,
      zfWeekVO: null,
      weekChart: null,
      historyChart: null,
    };
  },
  computed: {
    // 近七天平均覆盖率
    weekPro() {
      const value =
        this.active === "zf"
          ? this.zfWeekVO?.averageValue
          : this.xsWeekVO?.averageValue;
      if (typeof value === "string") {
        return Number(value).toFixed(2);
      }
      return value;
    },
    // 历史平均覆盖率
    historyPro() {
      const value =
        this.active === "zf"
          ? this.zfHistoryVO?.averageValue
          : this.xsHistoryVO?.averageValue;

      if (typeof value === "string") {
        return Number(value).toFixed(2);
      }
      return value;
    },
  },
  methods: {
    async onGetDashboardData(type) {
      try {
        const res = await getDashboardData(type);
        const { data } = res;
        if (this.active === "zf") {
          this.zfHistoryVO = data.zfHistoryVO;
          this.zfWeekVO = data.zfWeekVO;
        } else {
          this.xsHistoryVO = data.xsHistoryVO;
          this.xsWeekVO = data.xsWeekVO;
        }

        this.$nextTick(() => {
          this.initWeekChart();
          this.initHistoryChart();
        });
      } catch (error) {
        console.error(error);
        // 优先使用服务器返回的msg字段
        let errorMsg = "数据加载失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });
      }
    },

    initWeekChart() {
      const weekData =
        this.active === "zf"
          ? this.zfWeekVO?.infoList || []
          : this.xsWeekVO?.infoList || [];

      if (!this.weekChart) {
        this.weekChart = echarts.init(this.$refs.weekChart);
      }

      const option = {
        tooltip: {
          trigger: "axis",
          formatter: "{b}: {c}%",
        },
        grid: {
          left: "8%",
          right: "4%",
          bottom: "15%", // 增加底部空间,为文字换行提供位置
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: weekData.map((item) => this.formatDeptName(item.deptName)),
          axisLabel: {
            interval: 0,
            rotate: 45, // 增加旋转角度
            formatter: (value) => {
              // 每8个字符换一行
              return value.replace(/(.{8})/g, "$1\n");
            },
            width: 100,
            overflow: "break", // 文字超出时换行
            lineHeight: 12,
            fontSize: 11,
          },
        },
        yAxis: {
          type: "value",
          max: 100,
          axisLabel: {
            formatter: "{value}%",
          },
        },
        series: [
          {
            data: weekData.map((item) => parseFloat(item.coverageRate)),
            type: "bar",
            itemStyle: {
              color: "#0052D9",
            },
          },
        ],
      };

      this.weekChart.setOption(option);
    },

    initHistoryChart() {
      const historyData =
        this.active === "zf"
          ? this.zfHistoryVO?.infoList || []
          : this.xsHistoryVO?.infoList || [];

      if (!this.historyChart) {
        this.historyChart = echarts.init(this.$refs.historyChart);
      }

      const option = {
        tooltip: {
          trigger: "axis",
          formatter: "{b}: {c}%",
        },
        grid: {
          left: "8%",
          right: "4%",
          bottom: "15%", // 增加底部空间
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: historyData.map((item) => this.formatDeptName(item.deptName)),
          axisLabel: {
            interval: 0,
            rotate: 45, // 增加旋转角度
            formatter: (value) => {
              return value.replace(/(.{8})/g, "$1\n");
            },
            width: 100,
            overflow: "break",
            lineHeight: 12,
            fontSize: 11,
          },
        },
        yAxis: {
          type: "value",
          max: 100,
          axisLabel: {
            formatter: "{value}%",
          },
        },
        series: [
          {
            data: historyData.map((item) => parseFloat(item.coverageRate)),
            type: "line",
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(0,82,217,0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(0,82,217,0.1)",
                },
              ]),
            },
            itemStyle: {
              color: "#0052D9",
            },
            smooth: true,
          },
        ],
      };

      this.historyChart.setOption(option);
    },

    // 添加部门名称格式化方法
    formatDeptName(name) {
      if (!name) return "";
      // 移除重复的供电所/供电公司等后缀
      return name
        .replace(/供电(公司|所|服务站|中心)/g, "")
        .replace(/巡检/g, "巡")
        .trim();
    },
  },
  mounted() {
    this.onGetDashboardData(this.active);
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.weekChart) {
      this.weekChart.dispose();
    }
    if (this.historyChart) {
      this.historyChart.dispose();
    }
  },
};
</script>

<style scoped>
.board-container {
  height: 100vh;
  background-color: #fff;
}

.board-content {
  height: calc(100% - 44px);
  padding: 16px;
  box-sizing: border-box;
}

.coverage-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.coverage-wrapper {
  display: flex;
  justify-content: space-between;
}

.coverage-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.coverage-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.dot {
  width: 12px;
  height: 12px;
  background-color: #0052d9;
  border-radius: 50%;
  margin-right: 8px;
}

.title {
  font-size: 14px;
  color: #333;
}

.coverage-value {
  font-size: 32px;
  font-weight: 600;
  color: #0052d9;
  line-height: 1.2;
}

.charts-container {
  height: calc(100% - 130px);
}

.chart-section {
  height: 50%;
  padding: 16px;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.header-line {
  width: 6px;
  height: 16px;
  background-color: #4880ff;
  margin-right: 8px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.header-tag {
  font-size: 14px;
  color: #4880ff;
}

.chart-wrapper {
  height: calc(100% - 32px);
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
