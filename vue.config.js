module.exports = {
    css: {
        loaderOptions: {
            postcss: {
                plugins: [
                    require("tailwindcss"),
                    require("autoprefixer")
                ],
            },
        },
    },
    // 配置webpack
    configureWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 生产环境配置
            config.mode = 'production'
            config.optimization = {
                minimize: true,
                splitChunks: {
                    chunks: 'all',
                    cacheGroups: {
                        vendor: {
                            name: 'codepass-vendor',
                            test: /[\\/]node_modules[\\/]/,
                            priority: 10,
                            chunks: 'all'
                        },
                        common: {
                            name: 'codepass-common',
                            minChunks: 2,
                            priority: 5,
                            chunks: 'all'
                        }
                    }
                }
            }

            // 配置输出文件名，添加codepass前缀
            config.output = {
                ...config.output,
                filename: 'js/codepass-[name].[contenthash:8].js',
                chunkFilename: 'js/codepass-[name].[contenthash:8].js'
            }

            // 配置CSS文件名
            if (config.plugins) {
                config.plugins.forEach(plugin => {
                    if (plugin.constructor.name === 'MiniCssExtractPlugin') {
                        plugin.options.filename = 'css/codepass-[name].[contenthash:8].css'
                        plugin.options.chunkFilename = 'css/codepass-[name].[contenthash:8].css'
                    }
                })
            }

        } else {
            // 开发环境配置
            config.mode = 'development'
            config.devtool = 'source-map'
        }
    },

    // 配置静态资源
    chainWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 配置图片资源文件名
            if (config.module.rule('images').uses.has('url-loader')) {
                config.module
                    .rule('images')
                    .use('url-loader')
                    .tap(options => {
                        options = options || {}
                        options.name = 'img/codepass-[name].[contenthash:8].[ext]'
                        return options
                    })
            }

            // 配置字体文件名
            if (config.module.rule('fonts').uses.has('url-loader')) {
                config.module
                    .rule('fonts')
                    .use('url-loader')
                    .tap(options => {
                        options = options || {}
                        options.name = 'fonts/codepass-[name].[contenthash:8].[ext]'
                        return options
                    })
            }

            // 配置媒体文件名
            if (config.module.rule('media').uses.has('url-loader')) {
                config.module
                    .rule('media')
                    .use('url-loader')
                    .tap(options => {
                        options = options || {}
                        options.name = 'media/codepass-[name].[contenthash:8].[ext]'
                        return options
                    })
            }

            // 禁用source map生成，避免.map文件
            config.devtool(false)

            // 禁用source map相关插件，避免.map文件
            config.plugins.delete('preload')
            config.plugins.delete('prefetch')

            // 注释：为符合安全扫描要求，已移除自定义文件清理插件
            // 原因：该插件使用动态路径进行文件系统操作，被源码扫描工具标记为路径遍历风险
            // 如需清理构建文件，建议使用官方的 CleanWebpackPlugin 或在构建脚本中处理

        }
    }
}
