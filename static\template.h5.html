<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <!-- Open Graph data -->
    <!-- <meta property="og:title" content="Title Here" /> -->
    <!-- <meta property="og:url" content="http://www.example.com/" /> -->
    <!-- <meta property="og:image" content="http://example.com/image.jpg" /> -->
    <!-- <meta property="og:description" content="Description Here" /> -->
    <link
      rel="shortcut icon"
      type="image/x-icon"
      href="<%= BASE_URL %>static/favicon.ico"
    />
    <script>
      var coverSupport =
        "CSS" in window &&
        typeof CSS.supports === "function" &&
        (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"));
      document.write(
        '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no' +
          (coverSupport ? ", viewport-fit=cover" : "") +
          '" />'
      );
    </script>
    <link
      rel="stylesheet"
      href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css"
    />
  </head>

  <body>
    <noscript>
      <strong>Please enable JavaScript to continue.</strong>
    </noscript>
    <div id="app"></div>
    <script>
      // 禁用双击缩放
      document.addEventListener("gesturestart", function (e) {
        e.preventDefault();
      });
      document.addEventListener("dblclick", function (e) {
        e.preventDefault();
      });
      const config = {
        APP_INFO: "https://igw.sgcc.com.cn/connect/oauth2/authorize?", // i国网微信应用信息
        VUE_APP_OAUTH_URL: "", // 获取wx_code地址
        VUE_APP_TICKET_URL:
          "http://id.sgcc.com.cn:18088/eeee/identity/getAuthTicketByWechatCode", // 获取ISC票据地址
        VUE_APP_REDIRECT_URI: "zipapp://local.host/index.html", // 获取wx_code后重定向地址
        VUE_APP_RETRY_COUNT: 5, // 安全链接建立失败重试次数
        VUE_APP_APPNAME:
          "20211102111530914080008007111343$20180523164526158068060000170585$ANDR0ID", // 移动支撑平台的应用名
        VUE_APP_URL: "/istateinvoke-server/iStateGridInvoke", // 接口请求地址
        VUE_APP_RESOURCE: "masp", // 请求携带的资源指向
        VUE_APP_IS_LOCALHOST: 0, // 是否本地调试 1在线 0离线
        VUE_APP_FTPKEY: "", // 附件中转标识
        VUE_APP_TYPE: "prod", // 环境类型 prod生产环境 uat uat环境 dev开发环境
      };
      // window.config = config;
      // 使用Promise封装script加载
      function loadScript(src) {
        return new Promise((resolve, reject) => {
          const script = document.createElement("script");
          script.src = src;
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
      }

      // 检查wx对象是否完全就绪
      function checkWxReady(maxAttempts = 10) {
        return new Promise((resolve, reject) => {
          let attempts = 0;
          const check = () => {
            if (typeof wx !== "undefined" && wx.config) {
              resolve();
            } else if (attempts >= maxAttempts) {
              reject(new Error("wx对象初始化超时"));
            } else {
              attempts++;
              setTimeout(check, 100);
            }
          };
          check();
        });
      }

      // 加载 jweixin 脚本
      loadScript("<%= BASE_URL %>static/codepass-jweixin-1.6.0.js")
        .then(() => checkWxReady())
        .catch((error) => {
          console.error("jweixin-1.6.0.js loading failed:", error);
        });
    </script>
  </body>
</html>
