<template>
  <div class="page-container">
    <!-- 基础信息部分 -->
    <van-skeleton :loading="loading" :row="10" class="mb-4">
      <van-form ref="basicForm">
        <!-- 用户相关字段 - 只在走访类型时显示 -->
        <template v-if="formType === '1'">
          <!-- 必填项 -->
          <van-field
            v-model="formData.consNo"
            name="consNo"
            label="用户编号"
            placeholder="请输入用户编号"
            :rules="[{ required: true, message: '请输入用户编号' }]"
            required
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.consName"
            name="consName"
            label="用户名称"
            placeholder="请输入用户名称"
            :rules="[{ required: true, message: '请输入用户名称' }]"
            required
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.telNo"
            name="telNo"
            label="联系电话"
            type="number"
            placeholder="请输入联系电话"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.elecAddr"
            name="elecAddr"
            label="用电地址"
            placeholder="请输入用电地址"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
        </template>

        <!-- 巡视类型字段 - 台区编码必填且放在第一位 -->
        <template v-if="formType === '2'">
          <van-field
            v-model="formData.tgNo"
            name="tgNo"
            label="台区编码"
            placeholder="请输入台区编码"
            :rules="[{ required: true, message: '请输入台区编码' }]"
            required
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.tgName"
            name="tgName"
            label="台区名称"
            placeholder="请输入台区名称"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.orgNo"
            name="orgNo"
            label="供电单位编码"
            placeholder="请输入供电单位编码"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.orgName"
            name="orgName"
            label="供电单位名称"
            placeholder="请输入供电单位名称"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
        </template>

        <!-- 走访类型的通用字段 - 供电单位信息 -->
        <template v-if="formType === '1'">
          <van-field
            v-model="formData.orgNo"
            name="orgNo"
            label="供电单位编码"
            placeholder="请输入供电单位编码"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
          <van-field
            v-model="formData.orgName"
            name="orgName"
            label="供电单位名称"
            placeholder="请输入供电单位名称"
            :readonly="isFromScan"
            :input-align="isFromScan ? 'right' : 'left'"
          />
        </template>
      </van-form>
    </van-skeleton>

    <!-- 添加多个上传组件支持 -->
    <template v-if="uploadConfigs.length > 0">
      <van-cell-group
        v-for="config in uploadConfigs"
        :key="config.id"
        class="upload-section"
      >
        <van-cell :title="config.label" :required="config.required" />
        <div class="image-uploader p-4">
          <uni-file-picker
            v-model="imageListMap[config.id]"
            :auto-upload="false"
            fileMediatype="image"
            mode="grid"
            :limit="config.limit"
            @select="(e) => handleFileSelect(e, config.id)"
            @delete="(e) => handleDelete(e, config.id)"
            :sourceType="['album']"
            file-extname="jpg,png,jpeg"
          />
        </div>
      </van-cell-group>
    </template>

    <!-- 动态表单部分 -->
    <van-skeleton :loading="loading" :row="15" class="mb-4">
      <vm-form-render
        v-if="formReady"
        :form-json="formJson"
        :form-data="dynamicFormData"
        :globalDsv="globalDsv"
        :option-data="optionData"
        ref="vFormRef"
      />
    </van-skeleton>

    <!-- 提交按钮 -->
    <div v-if="formReady" class="submit-block">
      <div v-if="locationError" class="location-error">
        {{ locationError }}
      </div>
      <van-button
        block
        size="large"
        type="primary"
        :disabled="!geofenceCheckCompleted"
        @click="onSubmit"
        :loading="submitting"
        loading-text="提交中..."
      >
        <div class="button-content">
          <span class="submit-text">
            {{ geofenceCheckCompleted ? "提交" : "位置检测中..." }}
          </span>
          <div
            class="geofence-status"
            v-if="!locationError && geofenceCheckCompleted && !hideGeofence"
          >
            <span
              :class="{
                'geofence-inside': isInsideGeofence,
                'geofence-outside': !isInsideGeofence,
              }"
            >
              {{ isInsideGeofence ? "在指定区域内" : "不在指定区域内" }}
            </span>
            <van-icon
              :name="isInsideGeofence ? 'success' : 'cross'"
              :class="{
                'text-success': isInsideGeofence,
                'text-danger': !isInsideGeofence,
              }"
              size="14"
            />
          </div>
          <div class="geofence-status" v-if="!geofenceCheckCompleted">
            <van-icon name="loading" size="14" />
          </div>
        </div>
      </van-button>
    </div>
  </div>
</template>

<script>
import {
  getDefaultFormByNoOrder,
  submitFormByNoOrder,
  revokeForm,
  uploadImage,
  getLatitudeAndLongitude,
} from "@/api";
import { writeLog, LogLevel } from "@/utils/logger";
import { filterEmptyFields } from "@/utils/common";

export default {
  data() {
    return {
      formData: {
        orgNo: "", // 供电单位编码
        orgName: "", // 供电单位名称
        tgNo: "", // 台区编码
        tgName: "", // 台区名称
        consNo: "", // 用户编号
        consName: "", // 用户名称
        elecAddr: "", // 用电地址
        telNo: "", // 联系电话
        gridNo: "", // 网格员编号
        gridName: "", // 网格员名称
      },
      isFromScan: false, // 标记是否来自走访扫码
      fromSelfForm: false, // 标记是否来自自主填报
      formReady: false,
      formJson: null,
      optionData: null,
      dynamicFormData: {},
      globalDsv: {},
      formId: "",
      formDataId: "",
      loading: true,
      submitting: false,
      isSubmitted: false, // 添加提交状态标记
      uploadConfigs: [], // 存储所有上传组件的配置
      imageListMap: {}, // 存储每个上传组件的图片列表
      imageUrlsMap: {}, // 存储每个上传组件的图片URL列表
      // 添加电子围栏相关数据
      formLongitude: null,
      formLatitude: null,
      userLongitude: null,
      userLatitude: null,
      isInsideGeofence: false,
      locationError: null,
      geofenceRadius: 1000, // 电子围栏半径（米）
      geofenceCheckCompleted: false, // 电子围栏检测是否完成
      isGettingLocation: false, // 防止重复调用getUserLocation
      // 添加思极地图相关配置
      key: "29157aa48d4739d297756afb1395ea81",
      secret: "dcf32339ed5f398ba00358c38cf33e47",
      plugin: ["SGMap.GeolocationTask"],
      mapSDKLoaded: false,
      formType: "1", // 添加表单类型，默认为走访
      scanData: null, // 保存扫码数据
      hideGeofence: false, // 是否隐藏电子围栏功能
    };
  },
  methods: {
    // 设置页面标题
    setPageTitle() {
      const title =
        this.formType === "1"
          ? "走访自主填报"
          : this.formType === "2"
          ? "巡视自主填报"
          : "请填报";

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: title,
      });

      writeLog(LogLevel.INFO, "noform初始化", "设置页面标题", {
        formType: this.formType,
        title: title,
      });
    },

    async initFormData() {
      this.loading = true;
      try {
        writeLog(LogLevel.INFO, "noform初始化", "开始获取表单数据");
        const res = await getDefaultFormByNoOrder({ type: this.formType }); // 使用动态的formType
        writeLog(LogLevel.INFO, "noform初始化", "获取表单数据成功", {
          formId: res.data.formId,
          formDataId: res.data.formDataId,
        });
        const { formConfig, widgetList, optionData, formId, formDataId } =
          res.data;

        this.formId = formId;

        // 解析表单配置
        const parsedFormConfig = JSON.parse(formConfig);
        const rawWidgetList = JSON.parse(widgetList);
        const parsedOptionData = optionData ? JSON.parse(optionData) : [];
        this.formDataId = formDataId;

        // 使用新的方法处理上传组件
        const parsedWidgetList = this.extractUploadConfig(rawWidgetList);

        // 设置表单JSON数据
        this.formJson = {
          formConfig: parsedFormConfig,
          widgetList: parsedWidgetList,
        };

        // 转换选项数据
        this.optionData = parsedOptionData.reduce((acc, curr) => {
          if (curr.options && curr.options.name) {
            acc[curr.options.name] = curr.options.optionItems || [];
          }
          return acc;
        }, {});

        // 所有数据准备就绪后，设置formReady为true
        this.$nextTick(() => {
          this.formReady = true;
          this.loading = false;
        });
      } catch (error) {
        writeLog(LogLevel.ERROR, "noform初始化", "获取表单数据失败", {
          error: error.message,
        });
        console.error("获取表单数据失败:", error);

        // 优先使用服务器返回的msg字段
        let errorMsg = "获取表单数据失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },
    async validateBasicForm() {
      try {
        await this.$refs.basicForm.validate();
        return true;
      } catch (error) {
        // 巡视类型需要验证台区编码必填
        if (this.formType === "2") {
          // 检查台区编码是否已填写
          if (!this.formData.tgNo || this.formData.tgNo.trim() === "") {
            uni.showToast({
              title: "请输入台区编码",
              icon: "none",
            });
            return false;
          }
          return true;
        }
        return false;
      }
    },
    async validateDynamicForm() {
      try {
        const validationResult = await this.$refs.vFormRef.validate();
        return validationResult;
      } catch (error) {
        return false;
      }
    },
    // 添加新方法：验证上传组件
    validateUploadFields() {
      // 如果没有上传组件配置，直接返回true
      if (!this.uploadConfigs || this.uploadConfigs.length === 0) {
        return true;
      }

      // 添加日志，显示所有需要校验的上传组件
      console.log(
        "待校验的上传组件：",
        this.uploadConfigs
          .filter((config) => config.required)
          .map((config) => ({
            id: config.id,
            label: config.label,
            hasFiles: this.imageListMap[config.id]?.length > 0,
          }))
      );

      const invalidFields = this.uploadConfigs
        .filter(
          (config) =>
            config.required &&
            (!this.imageListMap[config.id] ||
              this.imageListMap[config.id].length === 0)
        )
        .map((config) => config.label);

      if (invalidFields.length > 0) {
        uni.showToast({
          title: `请上传${invalidFields.join("、")}`,
          icon: "none",
        });
        return false;
      }
      return true;
    },

    async onSubmit() {
      try {
        writeLog(LogLevel.INFO, "noform提交", "开始提交表单");

        // 验证基础表单
        const basicFormValid = await this.validateBasicForm();
        if (!basicFormValid) {
          uni.showToast({
            title: "请填写必填项",
            icon: "none",
          });
          return;
        }

        // 验证上传组件
        const uploadValid = this.validateUploadFields();
        if (!uploadValid) {
          return;
        }

        this.submitting = true;

        // 获取动态表单数据
        const dynamicFormData = await this.$refs.vFormRef.getFormData();

        // 添加所有上传组件的图片数据
        this.uploadConfigs.forEach((config) => {
          if (this.imageUrlsMap[config.id]?.length > 0) {
            dynamicFormData[config.id] = this.imageUrlsMap[config.id];
          }
        });

        // 过滤动态表单数据中的空值字段
        const filteredDynamicFormData = filterEmptyFields(dynamicFormData);

        // 构造提交数据 - 根据表单类型决定是否包含用户信息
        const customData =
          this.formType === "1"
            ? { ...this.formData } // 走访类型包含完整的用户信息
            : {
                // 巡视类型只包含台区和供电单位信息，确保使用tgNo字段
                orgNo: this.formData.orgNo,
                orgName: this.formData.orgName,
                tgNo: this.formData.tgNo, // 台区编码使用tgNo字段
                tgName: this.formData.tgName,
                gridNo: this.formData.gridNo,
                gridName: this.formData.gridName,
              };

        // 过滤自定义数据中的空值字段
        const filteredCustomData = filterEmptyFields(customData);

        const submitData = {
          // Syscustom 部分 - 基础表单数据
          custom: filteredCustomData,
          // SubmitFormDTO 部分 - 动态表单数据
          form: {
            formDataId: this.formDataId,
            formId: this.formId,
            formDataJson: JSON.stringify(filteredDynamicFormData),
            isOnSite: this.isInsideGeofence ? "1" : "0", // 添加是否在现场参数
          },
        };

        // 过滤整个提交数据对象
        const filteredSubmitData = filterEmptyFields(submitData);

        // 添加详细的提交数据日志
        writeLog(LogLevel.INFO, "noform提交", "提交数据详情", {
          formType: this.formType,
          isFromScan: this.isFromScan,
          customData: filteredCustomData,
          formData: {
            formDataId: this.formDataId,
            formId: this.formId,
            isOnSite: this.isInsideGeofence ? "1" : "0",
          },
          submitData: filteredSubmitData,
        });

        const res = await submitFormByNoOrder(filteredSubmitData);

        writeLog(LogLevel.INFO, "noform提交", "提交表单成功", {
          formDataId: this.formDataId,
          formId: this.formId,
          formType: this.formType,
        });
        this.isSubmitted = true; // 标记提交成功
        uni.showToast({
          title: "提交成功",
          icon: "success",
        });

        // 提交成功后的跳转逻辑
        setTimeout(() => {
          // 如果是从自主填报进入的，直接跳转到首页
          if (this.fromSelfForm) {
            uni.reLaunch({
              url: "/pages/index/index",
            });
          } else {
            // 其他情况返回上一页
            uni.navigateBack();
          }
        }, 1500);
      } catch (error) {
        writeLog(LogLevel.ERROR, "noform提交", "提交表单失败", {
          error: error.message,
        });
        console.error("提交表单失败:", error);

        // 优先使用服务器返回的msg字段
        let errorMsg = "提交失败,请检查填报信息";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }

        uni.showToast({
          title: errorMsg,
          icon: "none",
        });
      } finally {
        this.submitting = false;
      }
    },
    // 添加处理上传组件的方法
    extractUploadConfig(widgetList) {
      // 创建一个Set来记录需要移除的静态文本组件ID
      const staticTextToRemove = new Set();

      // 找到所有上传组件及其对应的标题
      const uploadWidgets = [];

      for (let i = 0; i < widgetList.length; i++) {
        const widget = widgetList[i];

        if (widget.type === "m-picture-upload" && widget.options?.uploadURL) {
          let label = widget.options.label || "图片上传";

          // 检查前一个组件是否是静态文本组件
          if (i > 0) {
            const prevWidget = widgetList[i - 1];
            if (
              prevWidget.type === "m-static-text" &&
              prevWidget.options?.textContent &&
              prevWidget.options.textContent.trim()
            ) {
              // 使用静态文本的内容作为上传组件的标题
              label = prevWidget.options.textContent.trim();
              // 标记这个静态文本组件需要被移除
              staticTextToRemove.add(prevWidget.id);

              console.log(`找到上传组件标题配对: "${label}" -> ${widget.id}`);
            }
          }

          uploadWidgets.push({
            ...widget,
            resolvedLabel: label,
          });
        }
      }

      // 添加日志，查看每个上传组件的必填状态
      uploadWidgets.forEach((widget) => {
        console.log("上传组件配置：", {
          id: widget.id,
          label: widget.resolvedLabel,
          originalLabel: widget.options.label,
          required: !!widget.options.required,
        });
      });

      // 初始化每个上传组件的配置
      this.uploadConfigs = uploadWidgets.map((widget) => ({
        id: widget.id,
        label: widget.resolvedLabel,
        required: !!widget.options.required,
        limit: parseInt(widget.options.limit || 3),
        fileTypes: widget.options.fileTypes || ["jpg", "jpeg", "png"],
        fileMaxSize: parseInt(widget.options.fileMaxSize || 5),
        uploadTip: widget.options.uploadTip || "",
      }));

      // 初始化图片列表映射
      this.uploadConfigs.forEach((config) => {
        this.imageListMap[config.id] = [];
        this.imageUrlsMap[config.id] = [];
      });

      // 深拷贝并过滤上传组件和已使用的静态文本组件
      return JSON.parse(
        JSON.stringify(
          widgetList.filter(
            (widget) =>
              widget.type !== "m-picture-upload" &&
              !staticTextToRemove.has(widget.id)
          )
        )
      );
    },

    // 添加文件处理相关方法
    // 修改 fileToBase64 方法
    fileToBase64(file) {
      return new Promise((resolve) => {
        // 直接使用文件内容作为Base64
        resolve(file.base64);
      });
    },

    async handleFileSelect(e, uploadId) {
      writeLog(LogLevel.INFO, "noform文件上传", "开始上传文件", {
        uploadId,
        fileCount: e.tempFiles?.length,
      });
      console.log("选择文件：", e, "组件ID：", uploadId);
      const { tempFiles } = e;
      if (!tempFiles) return;

      const config = this.uploadConfigs.find((c) => c.id === uploadId);
      if (!config) return;

      // 检查文件大小
      const maxSize = (config.fileMaxSize || 5) * 1024 * 1024;
      const isValid = tempFiles.every((file) => {
        if (file.size > maxSize) {
          uni.showToast({
            title: `文件大小不能超过${config.fileMaxSize}MB`,
            icon: "none",
          });
          return false;
        }
        return true;
      });

      if (!isValid) return;

      // 循环上传文件
      for (let i = 0; i < tempFiles.length; i++) {
        try {
          uni.showLoading({
            title: "上传中...",
            mask: true,
          });

          // 读取文件内容
          const base64Data = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              resolve(e.target.result); // 保留完整的 base64 字符串
            };
            reader.onerror = () => reject(new Error("读取文件失败"));
            reader.readAsDataURL(tempFiles[i].file);
          });

          if (!base64Data) {
            throw new Error("读取文件失败");
          }

          const res = await uploadImage({
            base64Data,
          });

          writeLog(LogLevel.INFO, "noform文件上传", "文件上传成功", {
            uploadId,
            url: res.url,
          });
          // 使用新的返回格式，直接使用 res.url
          const fileInfo = {
            name: tempFiles[i].name || `file_${Date.now()}`,
            url: res.url,
          };

          // 更新对应组件的图片列表
          this.imageUrlsMap[uploadId] = [
            ...(this.imageUrlsMap[uploadId] || []),
            fileInfo,
          ];
          this.imageListMap[uploadId] = [
            ...(this.imageListMap[uploadId] || []),
            {
              ...fileInfo,
              ...tempFiles[i],
            },
          ];
        } catch (error) {
          writeLog(LogLevel.ERROR, "noform文件上传", "文件上传异常", {
            uploadId,
            error: error.message,
          });
          console.error("上传失败：", error);

          // 优先使用服务器返回的msg字段
          let errorMsg = "上传失败";
          if (error.response?.msg) {
            errorMsg = error.response.msg;
          } else if (error.message) {
            errorMsg = error.message;
          }

          uni.showToast({
            title: errorMsg,
            icon: "none",
          });
        } finally {
          uni.hideLoading();
        }
      }
    },

    handleDelete(e, uploadId) {
      const { index } = e;
      this.imageUrlsMap[uploadId]?.splice(index, 1);
      this.imageListMap[uploadId]?.splice(index, 1);
    },

    // 添加地图脚本加载方法
    loadMapSDK() {
      return new Promise((resolve, reject) => {
        if (window.SGMap) {
          this.mapSDKLoaded = true;
          resolve();
          return;
        }

        const script = document.createElement("script");
        script.src = "https://map.sgcc.com.cn/maps?v=3.0.0";
        script.onload = () => {
          console.log("[位置服务] 思极地图脚本加载成功");
          this.mapSDKLoaded = true;
          resolve();
        };
        script.onerror = (error) => {
          console.error("[位置服务] 思极地图脚本加载失败:", error);
          reject(new Error("地图脚本加载失败"));
        };
        document.head.appendChild(script);
      });
    },

    // 添加计算两点距离的方法（Haversine公式）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const toRad = (value) => (value * Math.PI) / 180;
      const R = 6371000; // 地球半径，单位米
      const dLat = toRad(lat2 - lat1);
      const dLon = toRad(lon1 - lon2);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRad(lat1)) *
          Math.cos(toRad(lat2)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },

    // 通过接口获取目标经纬度
    async getTargetLocationFromAPI() {
      try {
        // 如果设置了隐藏电子围栏，直接跳过检测
        if (this.hideGeofence) {
          writeLog(
            LogLevel.INFO,
            "电子围栏检测",
            "隐藏电子围栏功能已启用，跳过电子围栏检测"
          );
          this.isInsideGeofence = true; // 默认允许提交
          this.geofenceCheckCompleted = true; // 设置检测完成状态
          return;
        }

        // 获取tgNo，从多个来源尝试获取
        let tgNo = null;

        // 1. 从表单数据中获取tgNo
        if (this.formData && this.formData.tgNo) {
          tgNo = this.formData.tgNo;
          writeLog(LogLevel.INFO, "电子围栏检测", "从表单数据中获取到tgNo", {
            tgNo: tgNo,
          });
        }

        // 2. 从扫码数据中获取tgNo（如果有的话）
        if (!tgNo && this.scanData && this.scanData.tgNo) {
          tgNo = this.scanData.tgNo;
          writeLog(LogLevel.INFO, "电子围栏检测", "从扫码数据中获取到tgNo", {
            tgNo: tgNo,
          });
        }

        // 如果没有tgNo，跳过接口调用（直接从首页进入的自主填报）
        if (!tgNo) {
          writeLog(
            LogLevel.INFO,
            "电子围栏检测",
            "未找到tgNo，跳过电子围栏检测（可能是直接从首页进入的自主填报）"
          );
          // 设置标记表示不需要电子围栏检测
          this.isInsideGeofence = true; // 默认允许提交
          this.geofenceCheckCompleted = true; // 设置检测完成状态
          return;
        }

        writeLog(LogLevel.INFO, "电子围栏检测", "开始调用接口获取目标经纬度", {
          tgNo: tgNo,
        });

        // 调用接口获取经纬度
        const response = await getLatitudeAndLongitude({
          tgNo: tgNo,
        });

        if (response && response.code === 200 && response.data) {
          const { longitude, latitude } = response.data;

          if (longitude && latitude) {
            // 更新目标经纬度
            this.formLongitude = parseFloat(longitude);
            this.formLatitude = parseFloat(latitude);

            writeLog(
              LogLevel.INFO,
              "电子围栏检测",
              "成功从接口获取目标经纬度",
              {
                longitude: this.formLongitude,
                latitude: this.formLatitude,
                tgNo: tgNo,
              }
            );
          } else {
            writeLog(
              LogLevel.WARN,
              "电子围栏检测",
              "接口返回的经纬度数据不完整",
              {
                longitude: longitude,
                latitude: latitude,
                tgNo: tgNo,
              }
            );
            // 如果接口返回数据不完整，设置错误提示
            this.locationError =
              "无法获取有效的位置信息，无法判断是否在指定区域内";
            this.geofenceCheckCompleted = true;
          }
        } else {
          writeLog(LogLevel.WARN, "电子围栏检测", "接口返回数据格式异常", {
            response: response,
            tgNo: tgNo,
          });
          // 如果接口返回异常，设置错误提示
          this.locationError = "获取位置信息接口异常，无法判断是否在指定区域内";
          this.geofenceCheckCompleted = true;
        }
      } catch (error) {
        writeLog(LogLevel.ERROR, "电子围栏检测", "调用获取经纬度接口失败", {
          error: error?.message || String(error),
          stack: error?.stack,
        });
        // 接口调用失败，设置错误提示
        this.locationError = "获取位置信息失败，无法判断是否在指定区域内";
        this.geofenceCheckCompleted = true;
      }
    },

    // 添加获取位置方法
    async getUserLocation() {
      // 如果设置了隐藏电子围栏，直接跳过位置检测
      if (this.hideGeofence) {
        writeLog(
          LogLevel.INFO,
          "电子围栏检测",
          "隐藏电子围栏功能已启用，跳过位置检测"
        );
        this.isInsideGeofence = true;
        this.geofenceCheckCompleted = true;
        return;
      }

      // 防止重复调用
      if (this.isGettingLocation) {
        writeLog(
          LogLevel.INFO,
          "电子围栏检测",
          "位置检测已在进行中，跳过重复调用"
        );
        return;
      }

      this.isGettingLocation = true;
      writeLog(LogLevel.INFO, "电子围栏检测", "开始获取位置");

      // 尝试通过接口获取目标经纬度
      await this.getTargetLocationFromAPI();

      // 如果没有目标经纬度，跳过位置检测
      if (!this.formLongitude || !this.formLatitude) {
        writeLog(LogLevel.INFO, "电子围栏检测", "没有目标经纬度，跳过位置检测");
        // 即使没有目标经纬度也要设置检测完成状态，允许用户提交
        this.geofenceCheckCompleted = true;
        this.isGettingLocation = false;
        return;
      }

      try {
        // 确保地图SDK已加载
        if (!this.mapSDKLoaded) {
          writeLog(LogLevel.INFO, "电子围栏检测", "地图SDK未加载，开始加载SDK");
          try {
            await this.loadMapSDK();
            writeLog(LogLevel.INFO, "电子围栏检测", "地图SDK加载成功");
          } catch (sdkError) {
            writeLog(LogLevel.ERROR, "电子围栏检测", "地图SDK加载失败", {
              error: sdkError?.message || String(sdkError),
              stack: sdkError?.stack,
            });
            throw new Error(
              `地图SDK加载失败: ${sdkError?.message || String(sdkError)}`
            );
          }
        }

        // 检查SGMap对象是否存在
        if (!window.SGMap) {
          writeLog(
            LogLevel.ERROR,
            "电子围栏检测",
            "SGMap对象不存在，SDK可能未正确加载"
          );
          throw new Error("SGMap对象不存在，SDK可能未正确加载");
        }

        writeLog(LogLevel.INFO, "电子围栏检测", "开始初始化思极地图", {
          key: this.key ? "已设置" : "未设置",
          secret: this.secret ? "已设置" : "未设置",
          plugin: JSON.stringify(this.plugin),
        });

        // 初始化思极地图
        try {
          await SGMap.tokenTask.login(this.key, this.secret);
          writeLog(LogLevel.INFO, "电子围栏检测", "思极地图登录成功");
        } catch (loginError) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "思极地图登录失败", {
            error: loginError?.message || String(loginError),
            stack: loginError?.stack,
          });
          throw new Error(
            `思极地图登录失败: ${loginError?.message || String(loginError)}`
          );
        }

        try {
          await SGMap.plugin(this.plugin);
          writeLog(LogLevel.INFO, "电子围栏检测", "思极地图插件加载成功");
        } catch (pluginError) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "思极地图插件加载失败", {
            error: pluginError?.message || String(pluginError),
            stack: pluginError?.stack,
          });
          throw new Error(
            `思极地图插件加载失败: ${
              pluginError?.message || String(pluginError)
            }`
          );
        }

        // 使用思极地图获取位置（带重试机制）
        writeLog(LogLevel.INFO, "电子围栏检测", "开始获取位置信息");
        let data;

        // 重试获取位置信息
        const maxRetries = 3;
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            writeLog(
              LogLevel.INFO,
              "电子围栏检测",
              `第${attempt}次尝试获取位置信息`
            );

            const geoTask = new SGMap.GeolocationTask({
              // 增加超时配置和精度设置
              timeout: 15000, // 15秒超时
              enableHighAccuracy: attempt === 1, // 第一次尝试高精度，后续降低精度提高成功率
              maximumAge: attempt === 1 ? 60000 : 300000, // 第一次缓存1分钟，后续5分钟
            });

            writeLog(
              LogLevel.INFO,
              "电子围栏检测",
              `GeolocationTask实例创建成功，第${attempt}次尝试配置`,
              {
                timeout: 15000,
                enableHighAccuracy: attempt === 1,
                maximumAge: attempt === 1 ? 60000 : 300000,
              }
            );

            // 使用Promise.race实现自定义超时控制
            const locationPromise = geoTask.getLocation();
            const timeoutDuration = attempt === 1 ? 20000 : 15000; // 第一次20秒，后续15秒
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => {
                reject(
                  new Error(`位置获取超时（${timeoutDuration / 1000}秒）`)
                );
              }, timeoutDuration);
            });

            writeLog(
              LogLevel.INFO,
              "电子围栏检测",
              `开始位置获取，最大等待${timeoutDuration / 1000}秒`
            );
            data = await Promise.race([locationPromise, timeoutPromise]);

            writeLog(
              LogLevel.INFO,
              "电子围栏检测",
              `第${attempt}次尝试位置信息获取成功`,
              {
                dataExists: !!data,
                dataLength: data ? data.length : 0,
                dataFirstItem: data && data[0] ? "存在" : "不存在",
              }
            );

            // 成功获取位置，跳出重试循环
            break;
          } catch (geoError) {
            lastError = geoError;
            writeLog(
              LogLevel.ERROR,
              "电子围栏检测",
              `第${attempt}次位置信息获取失败`,
              {
                error: geoError?.message || String(geoError),
                stack: geoError?.stack,
                errorType: geoError?.name || "Unknown",
                attempt: attempt,
                maxRetries: maxRetries,
              }
            );

            // 如果不是最后一次尝试，等待后重试
            if (attempt < maxRetries) {
              const waitTime = attempt * 2000; // 递增等待时间：2秒、4秒
              writeLog(
                LogLevel.INFO,
                "电子围栏检测",
                `等待${waitTime / 1000}秒后进行第${attempt + 1}次重试`
              );
              await new Promise((resolve) => setTimeout(resolve, waitTime));
            }
          }
        }

        // 如果所有重试都失败了
        if (!data) {
          writeLog(
            LogLevel.ERROR,
            "电子围栏检测",
            `所有${maxRetries}次重试均失败`,
            {
              finalError: lastError?.message || String(lastError),
            }
          );

          // 提供更友好的错误信息
          let errorMessage = "位置信息获取失败";
          if (
            lastError?.message?.includes("timeout") ||
            lastError?.message?.includes("超时")
          ) {
            errorMessage = "位置获取超时，请检查网络连接或稍后重试";
          } else if (
            lastError?.message?.includes("permission") ||
            lastError?.message?.includes("权限")
          ) {
            errorMessage = "位置权限被拒绝，请允许位置访问权限";
          }

          throw new Error(
            `${errorMessage}（已重试${maxRetries}次）: ${
              lastError?.message || String(lastError)
            }`
          );
        }

        if (!data || !data[0]) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "获取到的位置数据为空");
          throw new Error("获取位置信息失败，返回数据为空");
        }

        // 获取到的位置格式为 [longitude, latitude]
        writeLog(LogLevel.INFO, "电子围栏检测", "解析位置数据", {
          rawData: JSON.stringify(data[0]),
        });
        this.userLongitude = data[0][0];
        this.userLatitude = data[0][1];

        writeLog(LogLevel.INFO, "电子围栏检测", "开始计算距离");
        // 计算用户与表单中心点的距离
        const distance = this.calculateDistance(
          this.formLatitude,
          this.formLongitude,
          this.userLatitude,
          this.userLongitude
        );

        // 判断是否在电子围栏内
        this.isInsideGeofence = distance <= this.geofenceRadius;

        // 记录日志
        writeLog(LogLevel.INFO, "电子围栏检测", "位置检测结果", {
          formLat: this.formLatitude,
          formLng: this.formLongitude,
          userLat: this.userLatitude,
          userLng: this.userLongitude,
          distance,
          inGeofence: this.isInsideGeofence,
          geofenceRadius: this.geofenceRadius,
        });

        // 设置检测完成状态
        this.geofenceCheckCompleted = true;
        this.isGettingLocation = false;
      } catch (err) {
        console.error("位置获取过程出现异常:", err);
        this.locationError = "获取位置时发生错误，请稍后重试";
        this.isInsideGeofence = false;
        writeLog(LogLevel.ERROR, "电子围栏检测", "位置获取异常", {
          error: err?.message || String(err),
          stack: err?.stack,
          errorType: err?.constructor?.name || typeof err,
        });

        // 检查是否是网络错误
        if (
          err.message &&
          (err.message.includes("network") ||
            err.message.includes("网络") ||
            err.message.includes("timeout") ||
            err.message.includes("超时"))
        ) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "疑似网络问题", {
            message: err.message,
          });
        }

        // 检查是否是权限错误
        if (
          err.message &&
          (err.message.includes("permission") ||
            err.message.includes("权限") ||
            err.message.includes("denied") ||
            err.message.includes("拒绝"))
        ) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "疑似权限问题", {
            message: err.message,
          });
        }

        // 即使出错也要设置检测完成状态，允许用户提交
        this.geofenceCheckCompleted = true;
        this.isGettingLocation = false;
      }
    },
  },
  async onLoad(options) {
    console.log("[NoForm] onLoad: 页面加载，参数:", options);

    // 从页面参数中获取表单类型
    if (options.type) {
      this.formType = options.type;
      writeLog(LogLevel.INFO, "noform初始化", "从页面参数获取表单类型", {
        type: this.formType,
        description:
          this.formType === "1"
            ? "走访类型"
            : this.formType === "2"
            ? "巡视类型(台区编码必填)"
            : "未知类型",
      });

      // 设置页面标题
      this.setPageTitle();
    }

    // 检查是否来自自主填报
    if (options.fromSelfForm === "true") {
      this.fromSelfForm = true;
      writeLog(LogLevel.INFO, "noform初始化", "检测到来自自主填报");
    }

    // 检查是否需要隐藏电子围栏功能
    if (options.hideGeofence === "1") {
      this.hideGeofence = true;
      writeLog(
        LogLevel.INFO,
        "noform初始化",
        "检测到隐藏电子围栏参数，将隐藏电子围栏功能"
      );
    }

    // 处理扫码数据
    if (options && options.scanData) {
      try {
        // 解析传入的JSON字符串
        const scanData = JSON.parse(decodeURIComponent(options.scanData));
        console.log("[NoForm] onLoad: 解析扫码数据:", scanData);

        if (scanData.code === 200 && scanData.data) {
          // 走访扫码返回的客户信息
          const data = scanData.data;

          // 添加完整扫码数据结构日志
          writeLog(LogLevel.INFO, "noform扫码", "完整扫码数据结构", {
            scanDataStructure: {
              code: scanData.code,
              msg: scanData.msg,
              dataKeys: Object.keys(data),
              hasCodeId: !!data.codeId,
              codeIdValue: data.codeId,
            },
            fullScanData: scanData,
          });

          // 检查是否有客户信息（走访扫码）或台区信息（巡视扫码/自主填报）
          if (
            data.consNo ||
            data.consName ||
            data.tgNo ||
            data.tgName ||
            data.orgNo ||
            data.orgName ||
            data.telNo
          ) {
            console.log("[NoForm] onLoad: 检测到扫码信息");

            // 判断是走访扫码还是巡视自主填报
            const isVisitScan = data.consNo || data.consName || data.telNo;
            const isInspectSelfForm =
              !isVisitScan && (data.tgNo || data.tgName);

            if (isVisitScan) {
              console.log("[NoForm] onLoad: 检测到走访扫码客户信息");
              writeLog(
                LogLevel.INFO,
                "noform初始化",
                "检测到走访扫码客户信息，使用扫码数据"
              );
              // 走访扫码，保持原有逻辑
              this.formType = "1";
            } else if (isInspectSelfForm) {
              console.log("[NoForm] onLoad: 检测到巡视自主填报台区信息");
              writeLog(
                LogLevel.INFO,
                "noform初始化",
                "检测到巡视自主填报台区信息，使用扫码数据"
              );
              // 巡视自主填报，使用传递的type参数或默认为巡视类型
              // 注意：这里不强制设置formType，因为可能已经通过URL参数设置了
            }

            // 标记为扫码数据
            this.isFromScan = true;
            this.loading = true;

            // 保存扫码数据以供电子围栏检测使用
            this.scanData = data;

            // 设置页面标题
            this.setPageTitle();

            try {
              // 根据类型填充不同的表单数据
              if (isVisitScan) {
                // 走访扫码：填充客户信息
                this.formData = {
                  ...this.formData,
                  consNo: data.consNo || "",
                  consName: data.consName || "",
                  telNo: data.telNo || "",
                  tgNo: data.tgNo || "",
                  tgName: data.tgName || "",
                  orgNo: data.orgNo || "",
                  orgName: data.orgName || "",
                  elecAddr: data.address || "",
                  // 修复codeId获取逻辑：优先使用API返回的codeId，然后使用原始扫码内容，最后使用空字符串
                  codeId: data.codeId || scanData.originalCodeId || "",
                };

                // 添加详细的扫码数据日志
                writeLog(LogLevel.INFO, "noform扫码", "走访扫码数据处理", {
                  apiReturnedCodeId: data.codeId,
                  originalCodeId: scanData.originalCodeId,
                  finalCodeId: this.formData.codeId,
                  scanDataKeys: Object.keys(scanData),
                  dataKeys: Object.keys(data),
                  codeIdSource: data.codeId
                    ? "API返回"
                    : scanData.originalCodeId
                    ? "原始扫码"
                    : "默认空值",
                });
              } else if (isInspectSelfForm) {
                // 巡视自主填报：填充台区信息
                this.formData = {
                  ...this.formData,
                  tgNo: data.tgNo || "",
                  tgName: data.tgName || "",
                  orgNo: data.psNo || data.orgNo || "", // 使用psNo作为供电单位编码
                  orgName: data.psName || data.orgName || "", // 使用psName作为供电单位名称
                  gridNo: data.gridNo || "",
                  gridName: data.gridName || "",
                };
              }

              // 直接使用扫码返回的表单数据（如果有）
              if (data.formConfig && data.widgetList) {
                console.log("[NoForm] onLoad: 使用扫码返回的表单数据");

                // 设置表单ID和数据ID
                this.formId = data.formId;
                this.formDataId = data.formDataId;

                // 解析表单配置
                const parsedFormConfig = JSON.parse(data.formConfig);
                const rawWidgetList = JSON.parse(data.widgetList);
                const parsedOptionData = data.optionData
                  ? JSON.parse(data.optionData)
                  : [];

                // 使用新的方法处理上传组件
                const parsedWidgetList =
                  this.extractUploadConfig(rawWidgetList);

                // 设置表单JSON数据
                this.formJson = {
                  formConfig: parsedFormConfig,
                  widgetList: parsedWidgetList,
                };

                // 转换选项数据
                this.optionData = parsedOptionData.reduce((acc, curr) => {
                  if (curr.options && curr.options.name) {
                    acc[curr.options.name] = curr.options.optionItems || [];
                  }
                  return acc;
                }, {});

                // 所有数据准备就绪后，设置formReady为true
                this.$nextTick(() => {
                  this.formReady = true;
                  this.loading = false;
                });
              } else {
                console.log(
                  "[NoForm] onLoad: 扫码数据中没有表单配置，调用initFormData"
                );
                // 如果扫码数据中没有表单配置，则调用initFormData
                await this.initFormData();
              }

              // 获取用户位置
              this.getUserLocation();
            } catch (error) {
              console.error("[NoForm] onLoad: 处理走访扫码数据失败:", error);
              writeLog(LogLevel.ERROR, "noform初始化", "处理走访扫码数据失败", {
                error: error.message,
              });

              // 如果处理失败，回退到正常初始化
              await this.initFormData();
              // 确保在错误处理分支中也调用位置检测
              this.getUserLocation();
            } finally {
              this.loading = false;
            }

            return;
          }

          // 如果有表单渲染数据，按原逻辑处理
          const { formConfig, widgetList, optionData, formId, formDataId } =
            data;
          if (formId && formDataId) {
            this.formId = formId;
            this.formDataId = formDataId;

            // 解析表单配置
            const parsedFormConfig = JSON.parse(formConfig);
            const rawWidgetList = JSON.parse(widgetList);
            const parsedOptionData = optionData ? JSON.parse(optionData) : [];

            // 使用新的方法处理上传组件
            const parsedWidgetList = this.extractUploadConfig(rawWidgetList);

            // 设置表单JSON数据
            this.formJson = {
              formConfig: parsedFormConfig,
              widgetList: parsedWidgetList,
            };

            // 转换选项数据
            this.optionData = parsedOptionData.reduce((acc, curr) => {
              if (curr.options && curr.options.name) {
                acc[curr.options.name] = curr.options.optionItems || [];
              }
              return acc;
            }, {});

            // 如果有客户信息，填充到基础表单
            if (scanData.data.custom) {
              this.formData = {
                ...this.formData,
                ...scanData.data.custom,
              };
            }

            // 所有数据准备就绪后，设置formReady为true
            this.$nextTick(() => {
              this.formReady = true;
              this.loading = false;
            });

            // 获取用户位置
            this.getUserLocation();
            return;
          }
        }
      } catch (error) {
        console.error("[NoForm] onLoad: 解析扫码数据失败:", error);
        uni.showToast({
          title: "数据解析失败",
          icon: "error",
        });
      }
    }

    // 如果没有扫码数据或解析失败，则正常初始化表单
    this.initFormData();
    // 添加位置检测
    this.getUserLocation();
  },

  // 移除initFormData调用，改为在onLoad中处理
  created() {},
  onBackPress() {
    // 只在未提交且有 formDataId 时调用撤销
    if (!this.isSubmitted && this.formDataId) {
      try {
        // 添加日志
        writeLog(LogLevel.INFO, "noform撤销", "准备调用撤销接口", {
          formDataId: this.formDataId,
          isSubmitted: this.isSubmitted,
        });

        const params = {
          formDataId: this.formDataId,
        };
        revokeForm(params);
        writeLog(LogLevel.INFO, "noform撤销", "撤销调用成功");
      } catch (error) {
        writeLog(LogLevel.ERROR, "noform撤销", "撤销调用失败", {
          error: error.message,
          formDataId: this.formDataId,
        });
        console.error("撤销失败:", error);
      }
    }
    return false; // 返回 false 继续执行默认的返回逻辑
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  padding: 16px;
  padding-bottom: 100px; // 增加底部内边距，为提交按钮预留更多空间
}

.dynamic-form-wrapper {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.submit-block {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 999; // 添加较高的层级确保按钮在最上层
}

:deep(.van-skeleton) {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
}

:deep(.required-label) {
  &::before {
    content: "*";
    color: #ee0a24;
    margin-right: 4px;
    position: relative;
    top: 2px;
  }
}

.upload-section {
  background: #fff;
  border-radius: 8px;
  margin: 16px 0;
}

.image-uploader {
  background: #fff;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-text {
  font-size: 16px;
}

.geofence-status {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.geofence-inside {
  color: #e8f5e9;
}

.geofence-outside {
  color: #ffebee;
}

.text-success {
  color: #e8f5e9;
}

.text-danger {
  color: #ffebee;
}

.location-error {
  margin-bottom: 8px;
  padding: 6px 8px;
  font-size: 11px;
  color: #999;
  background: #f8f9fa;
  border-radius: 4px;
  text-align: center;
  opacity: 0.8;
}

:deep(.van-field--readonly) {
  background-color: #f8f8f8;
}
</style>
