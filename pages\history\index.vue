<template>
    <div class="w-full h-full p-2">
        <van-card v-for="(it, index) in historyList" :key="index" class="mb-2">
            <template #title>
                <div class="w-full my-2 flex justify-between">
                    <div class="text-[#333] flex justify-start">
                        <img src="@/static/images/box.svg" alt="box" class="mr-2" />
                        <div class="font-semibold text-md">
                            {{ it.planName }}
                        </div>
                    </div>
                    <div class="text-[#333] font-semibold text-md">
                        {{ it.customName }}
                    </div>
                </div>
            </template>
            <template #desc>
                <div class="w-full h-auto">
                    <div class="w-full pt-2 flex">
                        <div class="text-[#999] mr-2"> {{ it.appNo ? '申请' : '计划工单' }}编号:</div>
                        <div class="text-[#444]">
                            {{ it.appNo ? it.appNo : it.formNumber }}
                        </div>
                    </div>
                    <div class="w-full pt-2 flex">
                        <div class="text-[#999] mr-2"> 工单来源:</div>
                        <div class="text-[#444]">
                            {{ it.datasource ? getDatasource(it.datasource) : it.planName }}
                        </div>
                    </div>
                    <div class="w-full pt-2 flex">
                        <div class="text-[#999] mr-2"> 截止时间:</div>
                        <div class="text-[#3875F6]">
                            {{ it.endTime }}
                        </div>
                    </div>
                </div>
            </template>
        </van-card>
    </div>
</template>

<script>
export default {
    data() {
        return {
            historyList: []
        }
    },
    onLoad(options) {
        if (options.historyData) {
            const historyData = JSON.parse(decodeURIComponent(options.historyData))
            this.historyList = historyData.customForms || historyData.deviceForms || []
        }
    },
    methods: {
        getDatasource(it) {
            switch (it) {
                case '207107':
                    return it + '-服务申请接单下发'
                case '0207132':
                    return it + '-服务申请受理下发'
                case '0207077':
                    return it + '-业务上收-非故障类业务省接单下发'
                case '0207131':
                    return it + '-非故障受理下发'
                case '0207025':
                    return it + '-故障受理下发'
                default:
                    return it + '-服务申请接单下发'
            }
        }
    }
}
</script>
