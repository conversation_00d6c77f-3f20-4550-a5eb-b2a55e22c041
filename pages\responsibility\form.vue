<template>
  <div class="responsibility-form">
    <div class="form-header">
      <h1 class="form-title">{{ formName }}</h1>
      <p class="form-subtitle">请仔细填写表单中的内容</p>
    </div>

    <!-- 动态表单内容 -->
    <van-skeleton :loading="loading" :row="15" class="mb-4">
      <div class="form-content">
        <vm-form-render v-if="formReady" :form-json="formJson" :form-data="formData" :globalDsv="globalDsv"
          :option-data="optionData" ref="vFormRef" />
      </div>
    </van-skeleton>

    <div class="form-actions">
      <van-button block type="primary" @click="submitForm" :loading="submitting" :disabled="submitting">
        {{ submitting ? '提交中...' : '提交' }}
      </van-button>
    </div>
  </div>
</template>

<script>
import { Button, Skeleton } from "vant";
import { writeLog, LogLevel } from "@/utils/logger";
import { submitFormByNoOrder, getDefaultFormByNoOrder, revokeForm } from "@/api/index";
import { filterEmptyFields } from "@/utils/common";

export default {
  name: "ResponsibilityForm",
  components: {
    "van-button": Button,
    "van-skeleton": Skeleton,
  },
  data() {
    return {
      formId: "",
      formDataId: "",
      formName: "",
      formJson: {},
      formData: {},
      optionData: {},
      globalDsv: {},
      formReady: false,
      loading: true,
      submitting: false,
      deptId: "", // 存储从list页面传递过来的deptId
      isSubmitted: false, // 标记是否已经提交
    };
  },
  async onLoad(options) {
    await this.initFormData(options);
  },

  // 添加onBackPress生命周期，处理返回时的撤销逻辑
  onBackPress() {
    // 只在未提交且有formDataId时调用撤销
    if (!this.isSubmitted && this.formDataId) {
      try {
        // 添加日志
        writeLog(LogLevel.INFO, "服务履责表单撤销", "准备调用撤销接口", {
          formDataId: this.formDataId,
          isSubmitted: this.isSubmitted,
        });

        const params = {
          formDataId: this.formDataId,
        };
        revokeForm(params);
        writeLog(LogLevel.INFO, "服务履责表单撤销", "撤销调用成功");
      } catch (error) {
        writeLog(LogLevel.ERROR, "服务履责表单撤销", "撤销调用失败", {
          error: error.message,
          formDataId: this.formDataId,
        });
        console.error("撤销失败:", error);
      }
    }
    return false; // 返回false继续执行默认的返回逻辑
  },
  methods: {
    async initFormData(options) {
      try {
        writeLog(LogLevel.INFO, "服务履责表单页", "初始化表单数据", { options });

        this.formId = options.formId;
        this.formName = decodeURIComponent(options.formName || "");

        // 接收从list页面传递过来的deptId
        if (options.deptId) {
          this.deptId = decodeURIComponent(options.deptId);
        }

        // 接收从list页面传递过来的formDataId（如果有的话）
        if (options.formDataId) {
          this.formDataId = options.formDataId;
          writeLog(LogLevel.INFO, "服务履责表单页", "接收到传递的formDataId", {
            formDataId: this.formDataId
          });
        }

        // 记录接收到的额外参数
        const formNumber = options.formNumber ? decodeURIComponent(options.formNumber) : null;
        const formType = options.formType ? decodeURIComponent(options.formType) : null;

        writeLog(LogLevel.INFO, "服务履责表单页", "接收到的表单参数", {
          formId: this.formId,
          formName: this.formName,
          formNumber: formNumber,
          formType: formType,
          deptId: this.deptId,
          formDataId: this.formDataId,
          hasFormConfig: !!options.formConfig,
          hasWidgetList: !!options.widgetList,
          hasOptionData: !!options.optionData
        });

        // 调用接口获取表单数据 - 使用动态的formType参数
        writeLog(LogLevel.INFO, "服务履责表单页", "开始获取表单数据", {
          formType: formType,
          usingDefaultType: !formType
        });
        const formDataRes = await getDefaultFormByNoOrder({
          formId: this.formId,
          type: formType || "9"  // 使用动态的formType，如果没有则默认为"9"
        });

        if (formDataRes && formDataRes.data) {
          const responseData = formDataRes.data;

          // 设置formDataId
          if (responseData.formDataId) {
            this.formDataId = responseData.formDataId;
          }

          writeLog(LogLevel.INFO, "服务履责表单页", "获取表单数据成功", {
            formId: this.formId,
            formDataId: this.formDataId,
            hasFormConfig: !!responseData.formConfig,
            hasWidgetList: !!responseData.widgetList,
            hasOptionData: !!responseData.optionData
          });

          // 如果接口返回了表单配置，直接使用
          if (responseData.formConfig && responseData.widgetList) {
            const parsedFormConfig = JSON.parse(responseData.formConfig);
            const parsedWidgetList = JSON.parse(responseData.widgetList);
            const parsedOptionData = responseData.optionData ? JSON.parse(responseData.optionData) : [];

            // 设置表单JSON数据
            this.formJson = {
              formConfig: parsedFormConfig,
              widgetList: parsedWidgetList,
            };

            // 转换选项数据
            this.optionData = parsedOptionData.reduce((acc, curr) => {
              if (curr.options && curr.options.name) {
                acc[curr.options.name] = curr.options.optionItems || [];
              }
              return acc;
            }, {});

            // 所有数据准备就绪后，设置formReady为true
            this.$nextTick(() => {
              this.formReady = true;
              this.loading = false;
            });

            writeLog(LogLevel.INFO, "服务履责表单页", "表单数据初始化完成", {
              formId: this.formId,
              formDataId: this.formDataId,
              formName: this.formName
            });

            return; // 直接返回，不再执行后续的URL参数解析逻辑
          }
        } else {
          throw new Error("获取表单数据失败");
        }

        // 如果接口没有返回表单配置，尝试从URL参数中获取（备用方案）
        writeLog(LogLevel.INFO, "服务履责表单页", "接口未返回表单配置，尝试从URL参数获取");
        const formConfig = decodeURIComponent(options.formConfig || "{}");
        const widgetList = decodeURIComponent(options.widgetList || "[]");
        const optionData = decodeURIComponent(options.optionData || "[]");

        // 解析表单配置
        const parsedFormConfig = JSON.parse(formConfig);
        const parsedWidgetList = JSON.parse(widgetList);
        const parsedOptionData = JSON.parse(optionData);

        // 设置表单JSON数据
        this.formJson = {
          formConfig: parsedFormConfig,
          widgetList: parsedWidgetList,
        };

        // 转换选项数据
        this.optionData = parsedOptionData.reduce((acc, curr) => {
          if (curr.options && curr.options.name) {
            acc[curr.options.name] = curr.options.optionItems || [];
          }
          return acc;
        }, {});

        // 所有数据准备就绪后，设置formReady为true
        this.$nextTick(() => {
          this.formReady = true;
          this.loading = false;
        });

        writeLog(LogLevel.INFO, "服务履责表单页", "表单数据初始化完成", {
          formId: this.formId,
          formDataId: this.formDataId,
          formName: this.formName
        });

      } catch (error) {
        writeLog(LogLevel.ERROR, "服务履责表单页", "初始化表单数据失败", {
          error: error.message
        });
        console.error("初始化表单数据失败:", error);

        uni.showToast({
          title: "表单加载失败",
          icon: "none",
        });

        this.loading = false;
      }
    },

    async submitForm() {
      try {
        writeLog(LogLevel.INFO, "服务履责表单页", "开始提交表单");

        this.submitting = true;

        // 获取表单数据
        const formData = await this.$refs.vFormRef.getFormData();

        // 过滤表单数据中的空值字段
        const filteredFormData = filterEmptyFields(formData);
        const formDataJson = JSON.stringify(filteredFormData);

        // 构建提交参数 - 服务履责表单使用无单填报接口，参考走访表单结构
        const submitData = {
          form: {
            formDataId: this.formDataId,
            formId: this.formId,
            formDataJson: formDataJson,
          },
        };

        // 如果有deptId，添加到提交数据中
        if (this.deptId) {
          submitData.form.deptId = this.deptId;
        }

        // 过滤整个提交数据对象
        const filteredSubmitData = filterEmptyFields(submitData);

        writeLog(LogLevel.INFO, "服务履责表单页", "提交表单数据", {
          formId: this.formId,
          deptId: this.deptId,
          formData: filteredFormData,
          submitData: filteredSubmitData
        });

        const res = await submitFormByNoOrder(filteredSubmitData);

        writeLog(LogLevel.INFO, "服务履责表单页", "提交表单成功", res);

        // 标记为已提交，防止返回时调用撤销接口
        this.isSubmitted = true;

        uni.showToast({
          title: "提交成功",
          icon: "success",
          duration: 2000,
        });

        setTimeout(() => {
          uni.navigateBack();
        }, 2000);

      } catch (error) {
        writeLog(LogLevel.ERROR, "服务履责表单页", "提交表单失败", {
          error: error.message,
        });
        console.error("提交表单失败:", error);

        // 优先使用服务器返回的msg字段
        let errorMsg = "提交失败,请检查填写信息";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }

        uni.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.submitting = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.responsibility-form {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
  padding-bottom: 80px;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-title {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin: 0 0 10px 0;
}

.form-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.form-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 20px;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}
</style>