/**
 * 显示消息提示框
 * @param content 提示的标题
 */
export function toast(content) {
  uni.showToast({
    icon: "none",
    title: content,
  });
}

/**
 * 显示模态弹窗
 * @param content 提示的标题
 */
export function showConfirm(content) {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title: "提示",
      content: content,
      cancelText: "取消",
      confirmText: "确定",
      success: function (res) {
        resolve(res);
      },
    });
  });
}

/**
 * 参数处理
 * @param params 参数
 */
export function tansParams(params) {
  let result = "";
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && value !== "" && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (
            value[key] !== null &&
            value[key] !== "" &&
            typeof value[key] !== "undefined"
          ) {
            let params = propName + "[" + key + "]";
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result;
}

/**
 * 过滤对象中的空值字段（null、undefined、空字符串）
 * @param {Object} obj 需要过滤的对象
 * @param {boolean} filterEmptyString 是否过滤空字符串，默认为true
 * @returns {Object} 过滤后的对象
 */
export function filterEmptyFields(obj, filterEmptyString = true) {
  if (!obj || typeof obj !== "object") {
    return obj;
  }

  // 如果是数组，递归处理每个元素
  if (Array.isArray(obj)) {
    return obj
      .map((item) => filterEmptyFields(item, filterEmptyString))
      .filter((item) => {
        if (filterEmptyString) {
          return item !== null && item !== undefined && item !== "";
        } else {
          return item !== null && item !== undefined;
        }
      });
  }

  const filtered = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];

      // 过滤条件：排除null、undefined，以及可选的空字符串
      if (value === null || value === undefined) {
        continue; // 跳过null和undefined
      }

      if (filterEmptyString && value === "") {
        continue; // 跳过空字符串
      }

      // 如果值是对象或数组，递归处理
      if (typeof value === "object") {
        const filteredValue = filterEmptyFields(value, filterEmptyString);

        // 只有在过滤后的对象不为空时才添加
        if (Array.isArray(filteredValue)) {
          if (filteredValue.length > 0) {
            filtered[key] = filteredValue;
          }
        } else if (filteredValue && Object.keys(filteredValue).length > 0) {
          filtered[key] = filteredValue;
        }
      } else {
        // 基础类型直接添加
        filtered[key] = value;
      }
    }
  }

  return filtered;
}

/**
 * 深度过滤JSON字符串中的空值字段
 * @param {string} jsonString JSON字符串
 * @param {boolean} filterEmptyString 是否过滤空字符串，默认为true
 * @returns {string} 过滤后的JSON字符串
 */
export function filterEmptyFieldsInJson(jsonString, filterEmptyString = true) {
  try {
    const obj = JSON.parse(jsonString);
    const filtered = filterEmptyFields(obj, filterEmptyString);
    return JSON.stringify(filtered);
  } catch (error) {
    console.error("JSON解析失败:", error);
    return jsonString;
  }
}
