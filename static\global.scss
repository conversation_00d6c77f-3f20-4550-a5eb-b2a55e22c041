// 定义主题颜色变量
$primary-color: #00706B; // 国网绿
$primary-hover-color: #00a98f; // 稍亮的绿色
$info-color: #4a90e2; // 低饱和度蓝色
$error-color: #ff5252; // 低饱和度红色
$warning-color: #ffc107; // 低饱和度橙色
$text-color: #2c3e50; // 主要文字颜色
$border-color: #dfe4ed; // 边框颜色
$background-color: #fff; // 背景颜色

/* 全局样式 */
body {
  background-color: $background-color;
  color: $text-color;
}

/* 按钮 */
.van-button--primary {
  background-color: $primary-color !important;
  border-color: $primary-color !important;
  color: #fff !important;

  &:active {
    background-color: $primary-hover-color !important;
    border-color: $primary-hover-color !important;
  }
}

.van-button--info {
  background-color: $info-color !important;
  border-color: $info-color !important;
  color: #fff !important;
}

.van-button--danger {
  background-color: $error-color !important;
  border-color: $error-color !important;
  color: #fff !important;
}

.van-button--warning {
  background-color: $warning-color !important;
  border-color: $warning-color !important;
  color: #fff !important;
}

/* 导航栏 */
.van-nav-bar {
  background-color: $primary-color !important;
  color: #fff !important;
}

.van-nav-bar__title {
  color: #fff !important;
}

.van-nav-bar .van-icon {
  color: #fff !important;
}

/* 标签页 */
.van-tab--active {
  color: $primary-color !important;
}

.van-tabs__line {
  background-color: $primary-color !important;
}

/* 复选框 */
.van-checkbox__icon--checked .van-icon {
  background-color: $primary-color !important;
  border-color: $primary-color !important;
}

/* 单选框 */
.van-radio__icon--checked .van-icon {
  background-color: $primary-color !important;
  border-color: $primary-color !important;
}

/* 加载图标 */
.van-loading__spinner {
  color: $primary-color !important;
}

/* 开关 */
.van-switch--on {
  background-color: $primary-color !important;
}

/* 进度条 */
.van-progress__portion {
  background-color: $primary-color !important;
}

/* 信息提示 */
.van-toast--info {
  background-color: $info-color !important;
}

.van-toast--error {
  background-color: $error-color !important;
}

.van-toast--warning {
  background-color: $warning-color !important;
}

/* 对话框 */
.van-dialog__confirm-button {
  color: $primary-color !important;
}

.van-dialog__cancel-button {
  color: $info-color !important;
}

/* 通知栏 */
.van-notice-bar--info {
  background-color: rgba($info-color, 0.1) !important;
  color: $info-color !important;
}

.van-notice-bar--error {
  background-color: rgba($error-color, 0.1) !important;
  color: $error-color !important;
}

.van-notice-bar--warning {
  background-color: rgba($warning-color, 0.1) !important;
  color: $warning-color !important;
}

.pages-index{
  background-color: $background-color;
}