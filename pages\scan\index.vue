<template>
  <view class="container">
    <!-- 扫码器容器 -->
    <view class="scan-box" @click="handleTapToFocus">
      <!-- 隐藏的video元素，仅用于占位，实际扫码使用动态创建的原生video -->
      <video
        :id="scannerId"
        class="code-reader hidden-video"
        autoplay
        muted
        playsinline
        :controls="false"
        webkit-playsinline="true"
        x5-playsinline="true"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="false"
        style="display: none"
      ></video>

      <!-- 扫描框 - 根据扫码类型显示不同尺寸 -->
      <view
        class="scan-frame"
        :class="scanMode === 'visit' ? 'barcode-frame' : 'qrcode-frame'"
      >
        <view class="corner top-left"></view>
        <view class="corner top-right"></view>
        <view class="corner bottom-left"></view>
        <view class="corner bottom-right"></view>
      </view>

      <!-- 扫描提示 -->
      <view class="tip-text" v-if="!isScanning">
        <view>{{ scanTipText }}</view>
        <view class="code-tip">
          <span class="mode-restriction">{{ modeRestrictionText }}</span>
        </view>
      </view>

      <!-- 点击对焦提示 -->
      <view v-if="showFocusHint" class="focus-hint">
        <view class="focus-ring" :style="focusRingStyle"></view>
        <view class="focus-text">对焦中...</view>
      </view>
    </view>

    <!-- 成功效果 -->
    <view v-if="showSuccessEffect" class="success-effect">
      <view class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </view>
      <view class="success-text">扫码成功</view>
    </view>

    <!-- 控制按钮 -->
    <div class="control-buttons">
      <view class="camera-toggle-btn" @click="toggleCamera" v-if="isScanning">
        <text>切换摄像头</text>
      </view>

      <view
        class="flashlight-btn"
        :class="{ 'flashlight-on': flashlightOn }"
        @click="toggleFlashlight"
        v-if="isScanning && supportsTorch"
      >
        <text>{{ flashlightOn ? "🔦 关闭闪光灯" : "🔦 开启闪光灯" }}</text>
      </view>

      <!-- 手动输入按钮 (仅走访扫码显示) -->
      <view
        class="manual-input-btn"
        @click="showManualInput"
        v-if="scanMode === 'visit'"
      >
        <text>手动输入</text>
      </view>
    </div>

    <!-- 手动输入弹窗 (仅走访扫码) -->
    <view v-if="showManualInputDialog" class="manual-input-overlay">
      <view class="manual-input-dialog">
        <view class="dialog-title">手动输入条形码</view>
        <input
          v-model="manualCode"
          placeholder="请输入条形码"
          class="manual-input"
          type="text"
          @input="validateManualCode"
        />
        <view class="dialog-buttons">
          <button class="cancel-btn" @click="closeManualInput">取消</button>
          <button
            class="confirm-btn"
            @click="submitManualCode"
            :disabled="!isValidManualCode"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  BrowserMultiFormatOneDReader,
  BrowserQRCodeReader,
} from "@zxing/browser";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      codeReader: null,
      scanControls: null,
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      isInitializing: false,
      flashlightOn: false,
      supportsTorch: false,
      currentCameraId: null,
      availableCameras: [],
      currentCameraIndex: 0,

      // 扫码模式：'visit'(走访) 或 'inspect'(巡视) 或 'responsibility'(服务履责)
      scanMode: "visit",

      // 手动输入相关 (仅走访扫码)
      manualCode: "",
      isValidManualCode: false,
      showManualInputDialog: false,

      // 错误处理相关
      lastErrorMsg: null,
      errorCount: 0,

      // 对焦相关
      showFocusHint: false,
      focusRingStyle: {
        width: "100px",
        height: "100px",
        border: "2px dashed #fff",
        borderRadius: "50%",
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
      },
    };
  },

  computed: {
    // 扫码器ID
    scannerId() {
      return this.scanMode === "visit"
        ? "visit-barcode-reader"
        : "inspect-qr-reader";
    },

    // 扫码类型参数
    scanType() {
      if (this.scanMode === "visit") return "1";
      if (this.scanMode === "responsibility") return "2";
      return "0";
    },

    // 扫描提示文本
    scanTipText() {
      if (this.scanMode === "visit") return "将条形码放入框内进行扫描";
      if (this.scanMode === "responsibility") return "将二维码放入框内进行扫描";
      return "将二维码放入框内进行扫描";
    },

    // 模式限制文本
    modeRestrictionText() {
      if (this.scanMode === "visit") return "走访专用条形码扫描";
      if (this.scanMode === "responsibility") return "服务履责专用二维码扫描";
      return "巡视专用二维码扫描";
    },

    // 跳转页面URL
    targetPageUrl() {
      if (this.scanMode === "visit") return "/pages/no-form/index";
      if (this.scanMode === "responsibility") return "/pages/responsibility/entrance";
      return "/pages/order/index";
    },
  },

  onLoad(options) {
    console.log(`[UnifiedScan] 页面加载，参数:`, options);

    // 从URL参数获取扫码模式
    if (options.mode) {
      this.scanMode = options.mode;
    }

    console.log(`[UnifiedScan] 扫码模式: ${this.scanMode}`);
  },

  mounted() {
    console.log(`[UnifiedScan] mounted - 模式: ${this.scanMode}`);
    // 添加延迟确保 DOM 完全渲染
    setTimeout(() => {
      this.initializeScanner();
    }, 100);
  },

  beforeUnmount() {
    console.log(`[UnifiedScan] beforeUnmount`);
    this.isDestroyed = true;
    this.cleanup();
  },

  onUnload() {
    console.log(`[UnifiedScan] onUnload`);
    this.isDestroyed = true;
    this.cleanup();
  },

  onHide() {
    console.log(`[UnifiedScan] onHide`);
    this.stopScanning();
  },

  onShow() {
    console.log(`[UnifiedScan] onShow`);
    if (!this.isScanning && !this.isDestroyed) {
      setTimeout(() => {
        this.initializeScanner();
      }, 500);
    }
  },

  methods: {
    // 获取对应的扫码器实例
    getCodeReader() {
      if (this.scanMode === "visit") {
        // 走访扫码使用一维条形码扫描器，添加优化配置
        const codeReader = new BrowserMultiFormatOneDReader();

        // 条形码扫描优化配置
        const barcodeConfig = {
          // 提高扫描频率，条形码需要更频繁的扫描
          delayBetweenScanAttempts: 100, // 降低延迟，提高扫描频率
          delayBetweenScanSuccess: 500, // 成功后延迟

          // 条形码专用格式优化
          formats: [
            "CODE_128", // 最常用的条形码格式
            "CODE_39", // 工业标准条形码
            "CODE_93", // 高密度条形码
            "EAN_13", // 商品条形码
            "EAN_8", // 短商品条形码
            "UPC_A", // 美国商品条形码
            "UPC_E", // 短美国商品条形码
            "ITF", // 交叉25码
            "CODABAR", // 医疗/图书馆条形码
            "RSS_14", // GS1 DataBar
            "RSS_EXPANDED", // GS1 DataBar扩展版
          ],

          // 条形码识别增强
          tryHarder: true, // 启用更强力的识别算法
          returnCodabarStartEnd: true, // 返回CODABAR的起始结束字符
          assumeCode39CheckDigit: false, // 不假设CODE39有校验位
          assumeGS1: false, // 不假设GS1格式

          // 图像处理优化
          pureBarcode: false, // 不假设是纯条形码图像
          characterSet: "UTF-8", // 字符集

          // 条形码专用的图像增强
          possibleFormats: [
            "CODE_128",
            "CODE_39",
            "EAN_13",
            "EAN_8",
            "UPC_A",
            "UPC_E",
          ],
        };

        // 应用配置到扫码器
        if (codeReader.setHints) {
          codeReader.setHints(barcodeConfig);
        }

        console.log("[UnifiedScan] 条形码扫描器配置:", barcodeConfig);
        return codeReader;
      } else {
        // 巡视扫码使用二维码扫描器
        return new BrowserQRCodeReader();
      }
    },

    // 初始化扫码器
    async initializeScanner() {
      // 防止重复初始化
      if (this.isInitializing) {
        console.log(`[UnifiedScan] 正在初始化中，跳过重复调用`);
        return;
      }

      try {
        console.log(`[UnifiedScan] 初始化扫码器 - 模式: ${this.scanMode}`);
        this.isInitializing = true;

        // 如果已经有实例，先清理
        if (this.codeReader) {
          await this.cleanup();
        }

        // 创建对应的扫码器实例
        this.codeReader = this.getCodeReader();

        // 获取可用摄像头
        await this.getCameras();

        // 启动扫描
        await this.startScanning();
      } catch (error) {
        console.error(`[UnifiedScan] 初始化扫码器失败:`, error);
        uni.showToast({
          title: "扫码器初始化失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.isInitializing = false;
      }
    },

    // 获取可用摄像头
    async getCameras() {
      try {
        // 使用 @zxing/browser 的静态方法获取摄像头列表
        const { BrowserCodeReader } = await import("@zxing/browser");
        const cameras = await BrowserCodeReader.listVideoInputDevices();
        this.availableCameras = cameras || [];

        console.log(`[UnifiedScan] 获取到摄像头列表:`, this.availableCameras);

        // 优化的后置摄像头选择策略
        let backCameraIndex = -1;

        // 策略1：特定设备优化 - 优先选择"Video device 2"（用户设备的后置摄像头）
        backCameraIndex = this.availableCameras.findIndex(
          (camera) =>
            camera.label &&
            camera.label.toLowerCase().includes("video device 2")
        );

        // 策略2：通用后置摄像头关键字识别
        if (backCameraIndex === -1) {
          backCameraIndex = this.availableCameras.findIndex(
            (camera) =>
              camera.label &&
              (camera.label.toLowerCase().includes("back") ||
                camera.label.toLowerCase().includes("rear") ||
                camera.label.toLowerCase().includes("environment"))
          );
        }

        // 策略3：如果只有两个摄像头，选择第二个（索引1）作为后置摄像头
        if (backCameraIndex === -1 && this.availableCameras.length === 2) {
          backCameraIndex = 1;
          console.log(
            `[UnifiedScan] 检测到两个摄像头，选择第二个作为后置摄像头`
          );
        }

        // 策略4：如果有多个摄像头但前面策略都失败，选择索引为1的摄像头（通常是后置）
        if (backCameraIndex === -1 && this.availableCameras.length > 1) {
          backCameraIndex = 1;
        }

        // 策略5：最后回退，使用第一个可用摄像头
        if (backCameraIndex === -1 && this.availableCameras.length > 0) {
          backCameraIndex = 0;
        }

        if (backCameraIndex !== -1) {
          this.currentCameraIndex = backCameraIndex;
          this.currentCameraId =
            this.availableCameras[backCameraIndex].deviceId;
          console.log(
            `[UnifiedScan] 选择摄像头 (索引${backCameraIndex}):`,
            this.availableCameras[backCameraIndex]
          );
        } else {
          console.warn(`[UnifiedScan] 未找到可用摄像头`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 获取摄像头失败:`, error);
        uni.showToast({
          title: "获取摄像头失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 开始扫描
    async startScanning() {
      if (this.isScanning || this.isDestroyed || !this.codeReader) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 开始扫描 - 模式: ${this.scanMode}`);
        this.isScanning = true;

        // 等待 DOM 渲染完成
        await this.$nextTick();

        // 在 uni-app 中，直接创建一个新的 video 元素用于 @zxing/browser
        // 因为 uni-app 的 video 组件是封装的，无法直接被第三方库访问

        // 检查是否已存在原生 video 元素，如果存在则先清理
        let existingVideo = document.getElementById(this.scannerId + "-native");
        if (existingVideo) {
          console.log(`[UnifiedScan] 清理现有的原生 video 元素`);
          existingVideo.remove();
        }

        console.log(`[UnifiedScan] 创建原生 video 元素用于扫码`);
        const previewElem = document.createElement("video");
        previewElem.id = this.scannerId + "-native";
        previewElem.autoplay = true;
        previewElem.muted = true;
        previewElem.playsInline = true;
        // 隐藏所有视频播放相关的UI元素
        previewElem.controls = false;
        previewElem.controlsList = "nodownload nofullscreen noremoteplayback";
        previewElem.disablePictureInPicture = true;
        previewElem.style.width = "100%";
        previewElem.style.height = "100%";
        previewElem.style.objectFit = "cover";
        previewElem.style.position = "absolute";
        previewElem.style.top = "0";
        previewElem.style.left = "0";
        // 确保没有任何播放器UI显示
        previewElem.style.outline = "none";
        previewElem.style.border = "none";

        // uni-app 的 video 组件已经默认隐藏

        // 将原生 video 元素添加到容器中
        const container = document.querySelector(".scan-box");
        if (container) {
          container.appendChild(previewElem);
        } else {
          throw new Error("无法找到扫码容器");
        }

        console.log(`[UnifiedScan] 创建的原生视频元素:`, previewElem);

        // 扫码回调函数
        const scanCallback = (result, error) => {
          if (result) {
            // 扫码成功
            this.onScanSuccess(result.getText());
            return;
          }

          // 优化错误处理，只记录真正需要关注的错误
          if (error) {
            // NotFoundException 是正常的扫码过程中的错误，表示当前帧中没有找到二维码/条形码
            // 这类错误不需要记录，避免控制台日志过多
            if (error.name === "NotFoundException") {
              return;
            }

            // 只记录有意义的错误信息，避免 undefined 错误
            const errorMsg = error.message || error.name || "未知扫码错误";

            // 使用计数器避免相同错误重复输出
            if (!this.lastErrorMsg || this.lastErrorMsg !== errorMsg) {
              console.warn(`[UnifiedScan] 扫码错误:`, errorMsg);
              this.lastErrorMsg = errorMsg;
              this.errorCount = 1;
            } else {
              this.errorCount = (this.errorCount || 1) + 1;
              // 同一个错误超过5次就不再输出
              if (this.errorCount <= 5) {
                console.warn(
                  `[UnifiedScan] 扫码错误 (${this.errorCount}次):`,
                  errorMsg
                );
              }
            }
          }
        };

        // 尝试启动扫描，使用回退策略
        this.scanControls = await this.tryStartScanning(
          previewElem,
          scanCallback
        );

        console.log(`[UnifiedScan] 扫描已启动`);

        // 条形码专用的视频优化
        if (this.scanMode === "visit") {
          setTimeout(() => {
            this.applyBarcodeVideoOptimization();
          }, 1000); // 延迟1秒应用优化，确保摄像头已稳定启动
        }

        // 检查是否支持闪光灯
        this.checkTorchSupport();
      } catch (error) {
        console.error(`[UnifiedScan] 启动扫描失败:`, error);
        this.isScanning = false;

        uni.showToast({
          title: "启动扫描失败，请检查摄像头权限",
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 启动扫描，使用简化的策略
    async tryStartScanning(previewElem, scanCallback) {
      // 优先使用已选定的摄像头
      if (this.currentCameraId) {
        try {
          console.log(
            `[UnifiedScan] 使用选定的后置摄像头:`,
            this.currentCameraId
          );
          const controls = await this.codeReader.decodeFromVideoDevice(
            this.currentCameraId,
            previewElem,
            scanCallback
          );
          return controls;
        } catch (error) {
          console.warn(`[UnifiedScan] 选定摄像头启动失败:`, error);
        }
      }

      // 回退：尝试其他可用摄像头
      for (let i = 0; i < this.availableCameras.length; i++) {
        try {
          const camera = this.availableCameras[i];
          console.log(`[UnifiedScan] 尝试摄像头 ${i}:`, camera.label);

          const controls = await this.codeReader.decodeFromVideoDevice(
            camera.deviceId,
            previewElem,
            scanCallback
          );

          this.currentCameraId = camera.deviceId;
          this.currentCameraIndex = i;
          console.log(`[UnifiedScan] 成功启动摄像头 ${i}`);
          return controls;
        } catch (error) {
          console.warn(`[UnifiedScan] 摄像头 ${i} 启动失败:`, error);
        }
      }

      throw new Error("所有摄像头都无法启动");
    },

    // 停止扫描
    async stopScanning() {
      if (!this.isScanning) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 停止扫描`);

        // 停止扫描控制
        if (this.scanControls) {
          this.scanControls.stop();
          this.scanControls = null;
        }

        // 清理动态创建的原生 video 元素
        const nativeVideo = document.getElementById(this.scannerId + "-native");
        if (nativeVideo) {
          console.log(`[UnifiedScan] 清理原生 video 元素`);
          nativeVideo.remove();
        }

        // uni-app video 组件保持隐藏状态

        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
      } catch (error) {
        console.error(`[UnifiedScan] 停止扫描失败:`, error);
      }
    },

    // 扫码成功处理
    async onScanSuccess(decodedText) {
      const currentTime = Date.now();

      // 防止重复扫描（500ms内）
      if (currentTime - this.lastScanTime < 500 || this.isProcessing) {
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        console.log(`[UnifiedScan] 扫码成功:`, decodedText);

        // 验证扫码结果格式
        if (!this.validateScanResult(decodedText)) {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          uni.showToast({
            title: `请扫描有效的${codeType}`,
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证扫码结果
        const response = await getQrcode({
          codeId: decodedText, // 把扫到的二维码/条形码内容作为 codeId 传给接口
          type: this.scanType,
        });

        if (response && response.code === 200) {
          const codeType = this.scanMode === "visit" ? "条形码" : (this.scanMode === "responsibility" ? "二维码" : "二维码");
          console.log(`[UnifiedScan] ${codeType}验证成功:`, response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到对应页面，传递扫码数据
          setTimeout(() => {
            if (this.scanMode === "responsibility") {
              // 服务履责模式，跳转到服务履责入口页面
              // 增强响应数据，确保包含原始扫码的codeId
              const enhancedResponse = {
                ...response,
                originalCodeId: decodedText, // 保存原始扫码内容
              };
              
              const encodedData = encodeURIComponent(
                JSON.stringify(enhancedResponse)
              );
              
              uni.navigateTo({
                url: `${this.targetPageUrl}?scanData=${encodedData}`,
              });
            } else {
              // 走访和巡视模式，原有逻辑
              // 增强响应数据，确保包含原始扫码的codeId
              const enhancedResponse = {
                ...response,
                originalCodeId: decodedText, // 保存原始扫码内容
              };

              console.log(`[UnifiedScan] 增强响应数据:`, {
                originalCodeId: decodedText,
                apiReturnedCodeId: response.data?.codeId,
                enhancedResponse: enhancedResponse,
              });

              const encodedData = encodeURIComponent(
                JSON.stringify(enhancedResponse)
              );
              let targetUrl;

              if (this.scanMode === "visit") {
                // 走访扫码，跳转到no-form页面
                targetUrl = `${this.targetPageUrl}?scanData=${encodedData}`;
              } else {
                // 巡视扫码，跳转到order页面
                targetUrl = `${this.targetPageUrl}?scanData=${encodedData}`;
              }

              uni.navigateTo({
                url: targetUrl,
              });
            }
          }, 1000);
        } else {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          throw new Error(response?.msg || `${codeType}验证失败`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 处理扫码结果失败:`, error);

        const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
        uni.showToast({
          title: error.message || `${codeType}无效`,
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态，避免快速重复扫描
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 验证扫码结果格式
    validateScanResult(code) {
      if (!code || typeof code !== "string") {
        return false;
      }

      if (this.scanMode === "visit") {
        // 走访扫码：验证条形码格式（通常是数字）
        return /^[0-9]{8,}$/.test(code.trim());
      } else if (this.scanMode === "responsibility") {
        // 服务履责：二维码格式验证（更宽松）
        return code.trim().length > 0;
      } else {
        // 巡视扫码：二维码格式验证（更宽松）
        return code.trim().length > 0;
      }
    },



    // 检查闪光灯支持
    async checkTorchSupport() {
      try {
        // @zxing/browser 的闪光灯支持检查
        if (this.scanControls) {
          // 方法1：检查 controls 是否有 switchTorch 方法
          if (typeof this.scanControls.switchTorch === "function") {
            this.supportsTorch = true;
            console.log(
              `[UnifiedScan] 闪光灯支持: true (通过 switchTorch 方法)`
            );
            return;
          }

          // 方法2：通过视频轨道检查 torch 能力
          const stream = this.scanControls.stream;
          if (stream) {
            const track = stream.getVideoTracks()[0];
            if (track && track.getCapabilities) {
              const capabilities = track.getCapabilities();
              this.supportsTorch = capabilities && capabilities.torch === true;
              console.log(
                `[UnifiedScan] 闪光灯支持: ${this.supportsTorch} (通过视频轨道能力)`
              );
              return;
            }
          }
        }

        // 如果都检查不到，默认为不支持
        this.supportsTorch = false;
        console.log(`[UnifiedScan] 闪光灯支持: false (无法检测到支持)`);
      } catch (error) {
        console.warn(`[UnifiedScan] 检查闪光灯支持失败:`, error);
        this.supportsTorch = false;
      }
    },

    // 条形码专用视频优化
    async applyBarcodeVideoOptimization() {
      if (!this.scanControls || !this.scanControls.stream) {
        console.warn("[UnifiedScan] 无法应用条形码视频优化：缺少视频流");
        return;
      }

      try {
        const stream = this.scanControls.stream;
        const videoTrack = stream.getVideoTracks()[0];

        if (!videoTrack) {
          console.warn("[UnifiedScan] 无法应用条形码视频优化：缺少视频轨道");
          return;
        }

        // 获取摄像头能力
        const capabilities = videoTrack.getCapabilities();
        console.log("[UnifiedScan] 摄像头能力:", capabilities);

        // 构建条形码专用的约束
        const barcodeConstraints = {
          advanced: [],
        };

        // 1. 分辨率优化 - 条形码需要更高的分辨率来识别细节
        if (capabilities.width && capabilities.height) {
          // 优先使用高分辨率
          const idealWidth = Math.min(1920, capabilities.width.max || 1920);
          const idealHeight = Math.min(1080, capabilities.height.max || 1080);

          barcodeConstraints.width = { ideal: idealWidth, min: 1280 };
          barcodeConstraints.height = { ideal: idealHeight, min: 720 };

          console.log(
            `[UnifiedScan] 设置条形码分辨率: ${idealWidth}x${idealHeight}`
          );
        }

        // 2. 帧率优化 - 条形码需要适中的帧率，太高会影响处理速度
        if (capabilities.frameRate) {
          barcodeConstraints.frameRate = {
            ideal: 15, // 条形码最佳帧率
            min: 10, // 最低帧率
            max: 25, // 最高帧率，避免过高影响处理
          };
          console.log("[UnifiedScan] 设置条形码帧率: 15fps");
        }

        // 3. 对焦模式优化 - 条形码需要精确对焦
        if (capabilities.focusMode) {
          if (capabilities.focusMode.includes("continuous")) {
            barcodeConstraints.focusMode = "continuous";
            console.log("[UnifiedScan] 设置连续对焦模式");
          } else if (capabilities.focusMode.includes("single-shot")) {
            barcodeConstraints.focusMode = "single-shot";
            console.log("[UnifiedScan] 设置单次对焦模式");
          }
        }

        // 4. 对焦距离优化 - 条形码通常在中近距离扫描
        if (capabilities.focusDistance) {
          barcodeConstraints.focusDistance = {
            ideal: 0.5, // 中距离对焦，适合条形码扫描
            min: 0.3, // 最近对焦距离
            max: 1.0, // 最远对焦距离
          };
          console.log("[UnifiedScan] 设置条形码对焦距离: 0.5");
        }

        // 5. 曝光优化 - 条形码需要适当的曝光
        if (capabilities.exposureMode) {
          if (capabilities.exposureMode.includes("continuous")) {
            barcodeConstraints.exposureMode = "continuous";
            console.log("[UnifiedScan] 设置连续曝光模式");
          }
        }

        // 6. 亮度优化 - 条形码需要充足的亮度
        if (capabilities.brightness) {
          barcodeConstraints.brightness = {
            ideal: 0.6, // 稍高的亮度有助于条形码识别
            min: 0.4,
            max: 0.8,
          };
          console.log("[UnifiedScan] 设置条形码亮度: 0.6");
        }

        // 7. 对比度优化 - 条形码依赖高对比度
        if (capabilities.contrast) {
          barcodeConstraints.contrast = {
            ideal: 1.3, // 高对比度有助于条形码边缘识别
            min: 1.1,
            max: 1.5,
          };
          console.log("[UnifiedScan] 设置条形码对比度: 1.3");
        }

        // 8. 锐度优化 - 条形码需要清晰的边缘
        if (capabilities.sharpness) {
          barcodeConstraints.sharpness = {
            ideal: 1.5, // 高锐度确保条形码线条清晰
            min: 1.2,
            max: 1.8,
          };
          console.log("[UnifiedScan] 设置条形码锐度: 1.5");
        }

        // 9. 饱和度优化 - 条形码通常是黑白的，降低饱和度
        if (capabilities.saturation) {
          barcodeConstraints.saturation = {
            ideal: 0.8, // 略低的饱和度，突出黑白对比
            min: 0.6,
            max: 1.0,
          };
          console.log("[UnifiedScan] 设置条形码饱和度: 0.8");
        }

        // 10. 白平衡优化 - 确保黑白条纹对比清晰
        if (capabilities.whiteBalanceMode) {
          if (capabilities.whiteBalanceMode.includes("continuous")) {
            barcodeConstraints.whiteBalanceMode = "continuous";
            console.log("[UnifiedScan] 设置连续白平衡");
          }
        }

        // 应用约束
        console.log("[UnifiedScan] 应用条形码视频约束:", barcodeConstraints);
        await videoTrack.applyConstraints(barcodeConstraints);

        console.log("[UnifiedScan] 条形码视频优化应用成功");

        // 验证应用结果
        const settings = videoTrack.getSettings();
        console.log("[UnifiedScan] 当前视频设置:", settings);
      } catch (error) {
        console.warn("[UnifiedScan] 应用条形码视频优化失败:", error);
        // 优化失败不影响基本扫码功能
      }
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.isScanning || this.availableCameras.length <= 1) {
        console.warn(
          `[UnifiedScan] 无法切换摄像头: isScanning=${this.isScanning}, cameras=${this.availableCameras.length}`
        );
        return;
      }

      try {
        console.log(`[UnifiedScan] 开始切换摄像头`);

        // 显示切换提示
        uni.showToast({
          title: "正在切换摄像头...",
          icon: "loading",
          duration: 1000,
        });

        // 停止当前扫描
        await this.stopScanning();

        // 等待一段时间确保资源完全释放
        await new Promise((resolve) => setTimeout(resolve, 300));

        // 切换到下一个摄像头
        const oldCameraIndex = this.currentCameraIndex;
        this.currentCameraIndex =
          (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex].deviceId;

        console.log(
          `[UnifiedScan] 从摄像头 ${oldCameraIndex} 切换到摄像头 ${this.currentCameraIndex}:`,
          this.availableCameras[this.currentCameraIndex]
        );

        // 重新启动扫描
        await this.startScanning();

        // 显示成功提示
        uni.showToast({
          title: "摄像头切换成功",
          icon: "success",
          duration: 1000,
        });
      } catch (error) {
        console.error(`[UnifiedScan] 切换摄像头失败:`, error);

        // 尝试恢复到原来的摄像头
        try {
          console.log(`[UnifiedScan] 尝试恢复原摄像头`);
          await this.startScanning();
        } catch (recoveryError) {
          console.error(`[UnifiedScan] 恢复摄像头也失败:`, recoveryError);
        }

        uni.showToast({
          title: "切换摄像头失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      if (!this.supportsTorch || !this.scanControls) {
        console.warn(
          `[UnifiedScan] 闪光灯不可用: supportsTorch=${
            this.supportsTorch
          }, scanControls=${!!this.scanControls}`
        );
        return;
      }

      try {
        // 方法1：使用 @zxing/browser 推荐的 switchTorch 方法
        if (typeof this.scanControls.switchTorch === "function") {
          console.log(`[UnifiedScan] 使用 switchTorch 方法切换闪光灯`);
          await this.scanControls.switchTorch(!this.flashlightOn);
          this.flashlightOn = !this.flashlightOn;
          console.log(
            `[UnifiedScan] 闪光灯已${
              this.flashlightOn ? "开启" : "关闭"
            } (switchTorch)`
          );

          // 显示用户提示
          uni.showToast({
            title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
            icon: "none",
            duration: 1000,
          });
          return;
        }

        // 方法2：回退到传统的 applyConstraints 方法
        console.log(`[UnifiedScan] 使用 applyConstraints 方法切换闪光灯`);
        const stream = this.scanControls.stream;
        if (stream) {
          const track = stream.getVideoTracks()[0];
          if (track && track.applyConstraints) {
            const newTorchState = !this.flashlightOn;
            await track.applyConstraints({
              advanced: [{ torch: newTorchState }],
            });
            this.flashlightOn = newTorchState;
            console.log(
              `[UnifiedScan] 闪光灯已${
                this.flashlightOn ? "开启" : "关闭"
              } (applyConstraints)`
            );

            // 显示用户提示
            uni.showToast({
              title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
              icon: "none",
              duration: 1000,
            });
            return;
          }
        }

        throw new Error("无法找到可用的闪光灯控制方法");
      } catch (error) {
        console.error(`[UnifiedScan] 切换闪光灯失败:`, error);
        uni.showToast({
          title: "闪光灯操作失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 显示手动输入弹窗 (仅走访扫码)
    showManualInput() {
      if (this.scanMode !== "visit") {
        return;
      }
      this.showManualInputDialog = true;
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 关闭手动输入弹窗
    closeManualInput() {
      this.showManualInputDialog = false;
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 验证手动输入的条形码
    validateManualCode() {
      const code = this.manualCode.trim();
      this.isValidManualCode = /^[0-9]{8,}$/.test(code);
    },

    // 提交手动输入的条形码
    async submitManualCode() {
      if (!this.isValidManualCode || this.isProcessing) {
        return;
      }

      this.isProcessing = true;
      console.log({
        qrcode: this.manualCode,
        codeId: this.manualCode, // 把手动输入的条形码内容作为 codeId 传给接口
        type: this.scanType,
      });
      try {
        console.log(`[UnifiedScan] 手动输入条形码:`, this.manualCode);

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证条形码
        const response = await getQrcode({
          codeId: this.manualCode, // 把手动输入的条形码内容作为 codeId 传给接口
          type: this.scanType,
        });

        if (response && response.code === 200) {
          console.log(`[UnifiedScan] 手动输入条形码验证成功:`, response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到对应页面，传递扫码数据
          setTimeout(() => {
            // 增强响应数据，确保包含原始输入的codeId
            const enhancedResponse = {
              ...response,
              originalCodeId: this.manualCode, // 保存原始输入内容
            };

            console.log(`[UnifiedScan] 手动输入增强响应数据:`, {
              originalCodeId: this.manualCode,
              apiReturnedCodeId: response.data?.codeId,
              enhancedResponse: enhancedResponse,
            });

            const encodedData = encodeURIComponent(
              JSON.stringify(enhancedResponse)
            );
            let targetUrl;

            if (this.scanMode === "visit") {
              // 走访扫码，跳转到no-form页面
              targetUrl = `${this.targetPageUrl}?scanData=${encodedData}`;
            } else {
              // 巡视扫码，跳转到order页面
              targetUrl = `${this.targetPageUrl}?scanData=${encodedData}`;
            }

            uni.navigateTo({
              url: targetUrl,
            });
          }, 1000);
        } else {
          throw new Error(response?.msg || "条形码验证失败");
        }
      } catch (error) {
        console.error(`[UnifiedScan] 手动输入条形码处理失败:`, error);

        uni.showToast({
          title: error.message || "条形码无效",
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态
        setTimeout(() => {
          // 关闭弹窗
          this.closeManualInput();
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 清理资源
    async cleanup() {
      try {
        if (this.scanControls) {
          this.scanControls.stop();
          this.scanControls = null;
        }
        if (this.codeReader) {
          // @zxing/browser 不需要显式清理，但我们可以重置实例
          this.codeReader = null;
        }

        // 清理动态创建的原生 video 元素
        const nativeVideo = document.getElementById(this.scannerId + "-native");
        if (nativeVideo) {
          nativeVideo.remove();
        }

        // uni-app video 组件保持隐藏状态

        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
        console.log(`[UnifiedScan] 资源清理完成`);
      } catch (error) {
        console.error(`[UnifiedScan] 清理资源失败:`, error);
      }
    },

    // 处理点击对焦
    handleTapToFocus() {
      if (this.scanMode === "visit") {
        this.showFocusHint = true;
        setTimeout(() => {
          this.showFocusHint = false;
        }, 1000);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
}

.code-reader {
  width: 100%;
  height: 100%;
}

/* 隐藏所有视频播放相关的UI元素 */
.hidden-video {
  display: none !important;
}

/* 确保动态创建的video元素也没有播放器UI */
video[id$="-native"] {
  outline: none !important;
  border: none !important;
}

video[id$="-native"]::-webkit-media-controls {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-panel {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-play-button {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-timeline {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-current-time-display {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-time-remaining-display {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-mute-button {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-volume-slider {
  display: none !important;
}

video[id$="-native"]::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

.tip-text {
  position: absolute;
  bottom: 150px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  text-align: center;
  z-index: 10;
}

.code-tip {
  margin-top: 10px;
  font-size: 14px;
}

.mode-restriction {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  color: #ffd700;
}

.success-effect {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #4caf50;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.success-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.control-buttons {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 10;
}

.camera-toggle-btn,
.flashlight-btn,
.manual-input-btn {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.flashlight-btn.flashlight-on {
  background-color: rgba(255, 193, 7, 0.8);
  border-color: #ffc107;
  color: #000;
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.manual-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.manual-input-dialog {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 300px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.manual-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 16px;
}

.dialog-buttons {
  display: flex;
  gap: 10px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.confirm-btn {
  background-color: #1989fa;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  border: 2px solid #fff;
  border-radius: 10px;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #fff;
  border-radius: 50%;
}

.top-left {
  top: 0;
  left: 0;
}

.top-right {
  top: 0;
  right: 0;
}

.bottom-left {
  bottom: 0;
  left: 0;
}

.bottom-right {
  bottom: 0;
  right: 0;
}

.focus-hint {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.focus-ring {
  width: 100px;
  height: 100px;
  border: 2px dashed #fff;
  border-radius: 50%;
  margin-bottom: 20px;
}

.focus-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}
</style>
