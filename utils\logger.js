const STORAGE_KEY = 'app_logs'
const MAX_LOG_LENGTH = 1000

export const LogLevel = {
    DEBUG: 'DEBUG',
    INFO: 'INFO',
    WARN: 'WARN',
    ERROR: 'ERROR'
}

// 写入日志到 localStorage
export function writeLog(level, module, message, data = {}) {
    const timestamp = new Date().toISOString()
    const logEntry = {
        timestamp,
        level,
        module,
        message,
        data
    }

    // 控制台输出带颜色的日志
    const colors = {
        DEBUG: '#7F7F7F',
        INFO: '#008c87',
        WARN: '#FFA500',
        ERROR: '#FF0000'
    }

    console.log(
        `%c[${timestamp}] [${level}] [${module}] ${message}`,
        `color: ${colors[level]}`
    )

    if (Object.keys(data).length > 0) {
        console.log('%c相关数据:', 'color: #666666', data)
    }

    // 在生产环境下，可以将日志发送到服务器
    if (process.env.NODE_ENV === 'production') {
        try {
            // 存储日志到本地存储，限制最多存储100条
            const logs = JSON.parse(localStorage.getItem('app_logs') || '[]')
            logs.push(logEntry)
            while (logs.length > 100) {
                logs.shift()
            }
            localStorage.setItem('app_logs', JSON.stringify(logs))
        } catch (e) {
            console.error('存储日志失败:', e)
        }
    }

    return logEntry
}

export function getLogs() {
    return JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]')
}

export function clearLogs() {
    localStorage.removeItem(STORAGE_KEY)
}
