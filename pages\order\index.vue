<template>
  <div class="w-full bg-gray-50" style="padding-bottom: 160px">
    <!-- 台区信息 -->
    <div class="p-4">
      <div class="bg-white rounded-lg p-4 mb-4">
        <div class="text-base font-bold text-gray-800 mb-4">相关信息</div>
        <div v-if="orderDetail" class="text-sm">
          <!-- 台区基础信息 -->
          <div class="grid grid-cols-1 gap-2">
            <div class="flex py-2">
              <span class="w-32 text-gray-600">供电单位编码:</span>
              <span class="flex flex-1">{{ orderDetail.psNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">供电单位名称:</span>
              <span class="flex flex-1">{{ orderDetail.psName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区编码:</span>
              <span class="flex flex-1">{{ orderDetail.tgNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区名称:</span>
              <span class="flex flex-1">{{ orderDetail.tgName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区容量:</span>
              <span class="flex flex-1">{{ orderDetail.tgCap }}kVA</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区经理名称:</span>
              <span class="flex flex-1">{{ orderDetail.mngrName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区经理编号:</span>
              <span class="flex flex-1">{{
                orderDetail.distStaManagerNo
              }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">线路编号:</span>
              <span class="flex flex-1">{{ orderDetail.lineNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">线路名称:</span>
              <span class="flex flex-1">{{ orderDetail.lineName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">网格编号:</span>
              <span class="flex flex-1">{{ orderDetail.gridNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">网格名称:</span>
              <span class="flex flex-1">{{ orderDetail.gridName }}</span>
            </div>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100 my-4"></div>

          <!-- 巡视统计 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-gray-600 text-sm mb-1">日常巡视数量</div>
              <div class="text-xl font-bold text-blue-600">
                {{ orderDetail.dailyTourNum || 0 }}
              </div>
            </div>
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-gray-600 text-sm mb-1">特殊巡视数量</div>
              <div class="text-xl font-bold text-blue-600">
                {{ orderDetail.specialTourNum || 0 }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>

      <!-- 待处理工单 -->
      <div class="bg-white rounded-lg p-4 mb-4">
        <div class="text-base font-bold text-gray-800 mb-4">待处理工单</div>
        <template v-if="formDataStorageList && formDataStorageList.length > 0">
          <div
            v-for="(item, index) in formDataStorageList"
            :key="index"
            class="border-b border-gray-100 last:border-0 py-2"
          >
            <div class="flex justify-between items-center">
              <div>
                <div class="flex py-1">
                  <span class="w-20 text-gray-600">类型:</span>
                  <span class="text-gray-800">{{
                    getFormTypeLabel(item.formType)
                  }}</span>
                </div>
                <div class="flex py-1">
                  <span class="w-20 text-gray-600">状态:</span>
                  <span :class="getStatusClass(item.formStatus)">
                    {{ getFormStatusLabel(item.formStatus) }}
                  </span>
                </div>
              </div>
              <button
                @click="onFormSubmit(item)"
                class="bg-blue-600 text-white text-sm px-4 py-2 rounded-full"
              >
                点击填单
              </button>
            </div>
          </div>
        </template>
        <div v-else class="text-center py-8 text-gray-500">暂无待处理工单</div>
      </div>

      <!-- 历史工单（整合后只保留这一个） -->
      <!-- <div class="bg-white rounded-lg p-4 mb-4">
        <div class="text-base font-bold text-gray-800 mb-4">历史工单</div>
        <div class="grid grid-cols-2 gap-4">
          <div
            v-for="item in historyItems"
            :key="item.type"
            @click="onClickHistory(item)"
            class="bg-gray-50 rounded-lg p-3 text-center cursor-pointer"
          >
            <div class="text-xl font-bold text-blue-600">
              {{ item.count || 0 }}
            </div>
            <div class="text-sm text-gray-600">{{ item.label }}</div>
          </div>
        </div>
      </div> -->
    </div>

    <!-- 固定底部按钮栏 -->
    <div
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 safe-area-bottom"
    >
      <button
        @click="goToSelfForm"
        class="w-full bg-blue-600 text-white text-base py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
      >
        自主填报
      </button>
    </div>
  </div>
</template>

<script>
import { getHistoryInfoList, getDefaultFormByNoOrder } from "@/api";
import { writeLog, LogLevel } from "@/utils/logger";

export default {
  data() {
    return {
      orderDetail: null,
      scanData: null,
      historyItems: [
        { label: "日常巡视", type: "1", count: 0, formType: "1" },
        { label: "特殊巡视", type: "2", count: 0, formType: "2" },
      ],
      formDataStorageList: [], // 新增待处理工单列表
    };
  },
  async onLoad(options) {
    if (options.scanData) {
      try {
        const scanData = JSON.parse(decodeURIComponent(options.scanData));
        console.log("扫码数据:", scanData); // 添加日志
        this.scanData = scanData;
        this.orderDetail = scanData.data;

        // 初始化待处理工单列表
        this.formDataStorageList = this.orderDetail.formDataStorageList || [];

        // 设置历史工单数量
        if (this.orderDetail) {
          this.historyItems = [
            {
              label: "日常巡视",
              type: "1",
              count: this.orderDetail.dailyTourNum || 0,
              formType: "1",
            },
            {
              label: "特殊巡视",
              type: "2",
              count: this.orderDetail.specialTourNum || 0,
              formType: "2",
            },
          ];
        }
      } catch (error) {
        console.error("解析扫码数据失败:", error);
        uni.showToast({
          title: "获取数据失败",
          icon: "error",
        });
      }
    }
  },
  methods: {
    // 点击填单处理
    onFormSubmit(item) {
      writeLog(LogLevel.INFO, "电子围栏", "待处理工单填报开始", {
        formId: item.formId,
        formDataId: item.formDataId,
        orderDetail: this.orderDetail,
      });

      // 修改: 直接传递formId和formDataId参数，并添加tgNo用于电子围栏
      if (!item.formId || !item.formDataId) {
        writeLog(LogLevel.WARN, "电子围栏", "待处理工单数据不完整", { item });
        uni.showToast({
          title: "工单数据不完整",
          icon: "none",
        });
        return;
      }

      // 构建URL，包含电子围栏所需的tgNo参数
      const baseUrl = `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}`;
      const tgNo = this.orderDetail?.tgNo;

      if (tgNo) {
        const url = `${baseUrl}&tgNo=${tgNo}`;
        writeLog(LogLevel.INFO, "电子围栏", "待处理工单添加tgNo参数", {
          tgNo,
          finalUrl: url,
        });

        uni.navigateTo({ url });
      } else {
        writeLog(
          LogLevel.WARN,
          "电子围栏",
          "待处理工单缺少tgNo，无法启用电子围栏",
          {
            orderDetail: this.orderDetail,
          }
        );

        uni.navigateTo({ url: baseUrl });
      }
    },

    async onClickHistory(item) {
      if (!item.count) return;
      try {
        const params = {
          tgNo: this.orderDetail.tgNo,
          formType: item.formType,
        };
        const res = await getHistoryInfoList(params);
        const { data } = res;
        if (!data || !data.length) {
          uni.showToast({
            title: "暂无历史工单",
            icon: "none",
          });
          return;
        }
        uni.navigateTo({
          url: `/pages/history/index?historyData=${encodeURIComponent(
            JSON.stringify(data)
          )}`,
        });
      } catch (error) {
        // 优先使用服务器返回的msg字段
        let errorMsg = "获取历史工单失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });
      }
    },

    onBackPress() {
      uni.reLaunch({
        url: "/pages/index",
      });
      return true;
    },

    // 获取表单状态标签
    getFormStatusLabel(status) {
      const statusMap = {
        0: "超时未完成",
        1: "已创建",
        2: "进行中",
        3: "按时完成",
        4: "超时完成",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: "text-red-500",
        1: "text-blue-500",
        2: "text-green-500",
        3: "text-green-600",
        4: "text-orange-500",
      };
      return classMap[status] || "text-gray-500";
    },

    // 表单类型标签
    getFormTypeLabel(type) {
      const types = {
        1: "日常巡视",
        2: "特殊巡视",
        3: "日常走访",
        4: "特殊走访",
        5: "工单走访",
        6: "工单巡视",
        7: "默认走访",
        8: "默认巡视",
      };
      return types[type] || "未知类型";
    },

    // 跳转到自主填报页面
    goToSelfForm() {
      if (!this.orderDetail) {
        uni.showToast({
          title: "台区信息不完整",
          icon: "none",
        });
        return;
      }

      // 构造台区信息数据，模拟扫码数据格式但不包含表单配置
      const selfFormData = {
        code: 200,
        data: {
          psNo: this.orderDetail.psNo || "",
          psName: this.orderDetail.psName || "",
          tgNo: this.orderDetail.tgNo || "",
          tgName: this.orderDetail.tgName || "",
          gridNo: this.orderDetail.gridNo || "",
          gridName: this.orderDetail.gridName || "",
          lineNo: this.orderDetail.lineNo || "",
          lineName: this.orderDetail.lineName || "",
          mngrName: this.orderDetail.mngrName || "",
          distStaManagerNo: this.orderDetail.distStaManagerNo || "",
          tgCap: this.orderDetail.tgCap || "",
        },
      };

      // 跳转到自主填报页面，传递巡视类型和台区信息
      const encodedData = encodeURIComponent(JSON.stringify(selfFormData));
      uni.navigateTo({
        url: `/pages/no-form/index?type=2&scanData=${encodedData}&fromSelfForm=true`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.grid {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 640px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
