export const mockSSOParams = {
  code: "wx_code",
  dynamicPort: "30001",
  userInfo: {
    postBaseorgId: null,
    authToken: "B9C75E53-6D6E-4AD1-8ED4-356C8836FF08",
    useContact: "1",
    loginTime: "1735888763517",
    baseOrgNamePath:
      "/国家电网/国网湖北省电力有限公司/国网湖北省电力有限公司襄阳供电公司/国网湖北省电力有限公司襄阳供电公司本部/供电服务指挥中心/综合室",
    postLevel: "0",
    baseOrgId: "b93cba96ed0645acb7e5afcd12920b98",
    unitId: "EC2A77A950F65689E0430100007F864F",
    maspSecuritySmSwitch: "N",
    state: "1",
    unitName: "国网湖北省电力有限公司襄阳供电公司本部",
    baseOrgName: "综合室",
    saphrId: "********",
    provinceId: "hb",
    userId: "7c097ad0105d44468b18f321c60bbf26",
    nameCode: "hehao3",
    name: "何浩",
    showContact: "1",
    userType: "1",
    dispOrder: "********",
    account: "hehao3",
    baseOrgIdPath:
      "/1/EC2A77A9402D5689E0430100007F864F/EC2A77A943485689E0430100007F864F/EC2A77A950F65689E0430100007F864F/b52ef3cb760f4d65a88362bc7fa2ee22/b93cba96ed0645acb7e5afcd12920b98",
    iscId: "********************************",
    iscNo: "hehao3",
    loginName: "admin",
    orgNo: "42404",
    orgName: "襄阳供电公司",
    ORG_NO: "42404",
    ORG_NAME: "襄阳供电公司",
    deptNo: "************",
    DEPT_NO: "************",
    userNo: "hehao3",
    USER_NO: "hehao3",
    userName: "何浩",
    USER_NAME: "何浩",
    USER_ID: "7c097ad0105d44468b18f321c60bbf26",
    BUREAU_NO: "42404",
  },
};
